// Simple View Toggle Test
console.log('View Toggle Test Script Loaded');

// Test function to verify elements exist
function testViewToggle() {
    console.log('=== View Toggle Test ===');
    
    const elements = {
        tableView: document.getElementById('tableView'),
        cardView: document.getElementById('cardView'),
        viewIcon: document.getElementById('viewIcon'),
        viewText: document.getElementById('viewText'),
        toggleBtn: document.getElementById('toggleViewBtn')
    };
    
    console.log('Elements check:', elements);
    
    // Check if all elements exist
    const allElementsExist = Object.values(elements).every(el => el !== null);
    console.log('All elements exist:', allElementsExist);
    
    if (allElementsExist) {
        console.log('✅ All elements found - View toggle should work');
        
        // Test the toggle function
        if (typeof window.toggleView === 'function') {
            console.log('✅ toggleView function is available');
        } else {
            console.log('❌ toggleView function not found');
        }
    } else {
        console.log('❌ Some elements missing - View toggle may not work');
        
        // Show which elements are missing
        Object.entries(elements).forEach(([name, element]) => {
            if (!element) {
                console.log(`❌ Missing: ${name}`);
            }
        });
    }
}

// Run test when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', testViewToggle);
} else {
    testViewToggle();
}

// Manual toggle function for testing
window.manualToggle = function() {
    console.log('Manual toggle called');
    
    const tableView = document.getElementById('tableView');
    const cardView = document.getElementById('cardView');
    
    if (tableView && cardView) {
        if (tableView.style.display === 'none') {
            tableView.style.display = 'block';
            cardView.style.display = 'none';
            console.log('Switched to table view');
        } else {
            tableView.style.display = 'none';
            cardView.style.display = 'block';
            console.log('Switched to card view');
        }
    } else {
        console.log('Elements not found for manual toggle');
    }
};
