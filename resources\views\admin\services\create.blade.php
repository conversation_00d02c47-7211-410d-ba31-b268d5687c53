@extends('layouts.admin')

@section('title', 'เพิ่มบริการใหม่ - ระบบจัดการ')

@section('breadcrumb')
<li class="breadcrumb-item"><a href="{{ route('admin.services') }}">จัดการบริการ</a></li>
<li class="breadcrumb-item active">เพิ่มบริการใหม่</li>
@endsection

@section('content')
<div class="content-safe-area">
<!-- Header Section - เรียบง่าย -->
<div class="row mb-4">
    <div class="col-12">
        <div class="bg-white rounded-3 p-4 border">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-1 text-primary">
                        <i class="fas fa-plus me-2"></i>เพิ่มบริการใหม่
                    </h1>
                    <p class="text-muted mb-0">สร้างบริการใหม่พร้อมรายละเอียดและแกลเลอรี่รูปภาพ</p>
                </div>
                <div class="d-flex gap-2">
                    <a href="{{ route('admin.services') }}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-2"></i>กลับไปบริการ
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-body">
                <form action="{{ route('admin.services.store') }}" method="POST" enctype="multipart/form-data">
                    @csrf
                    
                    <div class="mb-3">
                        <label for="title" class="form-label">ชื่อบริการ <span class="text-danger">*</span></label>
                        <input type="text" class="form-control @error('title') is-invalid @enderror" 
                               id="title" name="title" value="{{ old('title') }}" required>
                        @error('title')
                        <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    
                    <div class="mb-3">
                        <label for="description" class="form-label">คำอธิบายสั้น <span class="text-danger">*</span></label>
                        <textarea class="form-control @error('description') is-invalid @enderror" 
                                  id="description" name="description" rows="3" required>{{ old('description') }}</textarea>
                        @error('description')
                        <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    
                    <div class="mb-3">
                        <label for="details" class="form-label">รายละเอียด</label>
                        <textarea class="form-control @error('details') is-invalid @enderror" 
                                  id="details" name="details" rows="5">{{ old('details') }}</textarea>
                        @error('details')
                        <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    
                    <div class="mb-3">
                        <label for="sort_order" class="form-label">ลำดับการแสดง</label>
                        <input type="number" class="form-control @error('sort_order') is-invalid @enderror"
                               id="sort_order" name="sort_order" value="{{ old('sort_order', 0) }}" min="0">
                        @error('sort_order')
                        <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    
                    <div class="mb-3">
                        <label for="image" class="form-label">รูปภาพหลัก (รูปปก)</label>
                        <input type="file" class="form-control @error('image') is-invalid @enderror"
                               id="image" name="image" accept="image/*">
                        @error('image')
                        <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                        <div class="form-text">รองรับไฟล์: JPG, PNG, GIF ขนาดไม่เกิน 2MB (รูปนี้จะเป็นรูปปกของบริการ)</div>
                    </div>

                    <div class="mb-3">
                        <label for="gallery_images" class="form-label">รูปภาพแกลเลอรี่ <span class="text-muted">(เพิ่มเติม)</span></label>
                        <input type="file" class="form-control @error('gallery_images.*') is-invalid @enderror"
                               id="gallery_images" name="gallery_images[]" accept="image/*" multiple>
                        @error('gallery_images.*')
                        <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                        <div class="form-text">
                            <i class="fas fa-info-circle text-primary me-1"></i>
                            สามารถเลือกหลายรูปพร้อมกัน (Ctrl+Click หรือ Shift+Click) | รองรับไฟล์: JPG, PNG, GIF ขนาดไม่เกิน 2MB ต่อรูป
                        </div>

                        <!-- Preview Area -->
                        <div id="gallery-preview" class="mt-3" style="display: none;">
                            <label class="form-label">ตัวอย่างรูปที่เลือก:</label>
                            <div class="row g-2" id="preview-container"></div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="is_active" name="is_active" 
                                   value="1" {{ old('is_active', true) ? 'checked' : '' }}>
                            <label class="form-check-label" for="is_active">
                                เปิดใช้งาน
                            </label>
                        </div>
                    </div>
                    
                    <div class="d-flex gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>บันทึก
                        </button>
                        <a href="{{ route('admin.services') }}" class="btn btn-secondary">ยกเลิก</a>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">คำแนะนำ</h5>
            </div>
            <div class="card-body">
                <ul class="list-unstyled">
                    <li class="mb-2">
                        <i class="fas fa-lightbulb text-warning me-2"></i>
                        ใช้ชื่อบริการที่ชัดเจนและเข้าใจง่าย
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-lightbulb text-warning me-2"></i>
                        เขียนคำอธิบายที่กระชับและน่าสนใจ
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-lightbulb text-warning me-2"></i>
                        ใส่รายละเอียดที่ครบถ้วนในส่วนรายละเอียด
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-lightbulb text-warning me-2"></i>
                        ใช้รูปภาพที่มีคุณภาพและเกี่ยวข้องกับบริการ
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-lightbulb text-warning me-2"></i>
                        ตั้งลำดับการแสดงเพื่อจัดเรียงบริการ
                    </li>
                    <li>
                        <i class="fas fa-images text-info me-2"></i>
                        สามารถเพิ่มรูปแกลเลอรี่หลายรูปได้
                    </li>
                </ul>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Gallery images preview
    const galleryInput = document.getElementById('gallery_images');
    const previewArea = document.getElementById('gallery-preview');
    const previewContainer = document.getElementById('preview-container');

    if (galleryInput) {
        galleryInput.addEventListener('change', function(e) {
            const files = Array.from(e.target.files);

            if (files.length > 0) {
                previewArea.style.display = 'block';
                previewContainer.innerHTML = '';

                files.forEach((file, index) => {
                    if (file.type.startsWith('image/')) {
                        const reader = new FileReader();

                        reader.onload = function(e) {
                            const col = document.createElement('div');
                            col.className = 'col-md-2 col-4';

                            col.innerHTML = `
                                <div class="card">
                                    <img src="${e.target.result}" class="card-img-top"
                                         style="height: 100px; object-fit: cover;"
                                         alt="Preview ${index + 1}">
                                    <div class="card-body p-2">
                                        <small class="text-muted">รูปที่ ${index + 1}</small>
                                    </div>
                                </div>
                            `;

                            previewContainer.appendChild(col);
                        };

                        reader.readAsDataURL(file);
                    }
                });
            } else {
                previewArea.style.display = 'none';
            }
        });
    }

    // Main image preview
    const mainImageInput = document.getElementById('image');
    if (mainImageInput) {
        mainImageInput.addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file && file.type.startsWith('image/')) {
                const reader = new FileReader();

                reader.onload = function(e) {
                    // Remove existing preview
                    const existingPreview = document.getElementById('main-image-preview');
                    if (existingPreview) {
                        existingPreview.remove();
                    }

                    // Create new preview
                    const preview = document.createElement('div');
                    preview.id = 'main-image-preview';
                    preview.className = 'mt-2';
                    preview.innerHTML = `
                        <img src="${e.target.result}" class="img-thumbnail"
                             style="max-width: 200px; max-height: 150px;"
                             alt="ตัวอย่างรูปหลัก">
                        <div class="form-text">ตัวอย่างรูปหลัก</div>
                    `;

                    mainImageInput.parentNode.appendChild(preview);
                };

                reader.readAsDataURL(file);
            }
        });
    }

    // Form validation
    const form = document.querySelector('form');
    if (form) {
        form.addEventListener('submit', function(e) {
            const title = document.getElementById('title').value.trim();
            const description = document.getElementById('description').value.trim();

            if (!title || !description) {
                e.preventDefault();
                alert('กรุณากรอกข้อมูลให้ครบถ้วน');
                return false;
            }
        });
    }
});
</script>
</div> <!-- ปิด content-safe-area -->
@endsection
