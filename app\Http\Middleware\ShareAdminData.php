<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use App\Models\Contact;
use Illuminate\Support\Facades\View;

class ShareAdminData
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next)
    {
        // Share unread contacts count with all admin views
        if ($request->is('admin/*')) {
            $unread_contacts = Contact::unread()->count();
            View::share('unread_contacts', $unread_contacts);
        }

        return $next($request);
    }
}
