/**
 * Stable Modal Gallery System
 * แก้ปัญหา modal backdrop ค้างและทำให้ระบบแกลเลอรี่เสถียรขึ้น
 */

class StableModalGallery {
    constructor(modalId = 'imageModal') {
        this.modalId = modalId;
        this.modalElement = document.getElementById(modalId);
        this.currentIndex = 0;
        this.images = [];
        this.modalInstance = null;
        
        if (this.modalElement) {
            this.init();
        }
    }
    
    init() {
        this.collectImages();
        this.bindEvents();
        this.addStyles();
    }
    
    collectImages() {
        // Collect images from gallery thumbnails first, then modal thumbnails
        const galleryThumbs = document.querySelectorAll('.gallery-thumbnail');
        const modalThumbs = this.modalElement.querySelectorAll('.modal-thumb');

        if (galleryThumbs.length > 0) {
            // Use gallery thumbnails as primary source
            this.images = Array.from(galleryThumbs).map((thumb, index) => ({
                src: thumb.dataset.image,
                alt: thumb.dataset.caption || thumb.alt || '',
                index: index
            }));
        } else if (modalThumbs.length > 0) {
            // Fallback to modal thumbnails
            this.images = Array.from(modalThumbs).map((thumb, index) => ({
                src: thumb.dataset.image,
                alt: thumb.dataset.alt || '',
                index: index
            }));
        }
    }
    
    bindEvents() {
        // Close button
        const closeBtn = this.modalElement.querySelector('.btn-close');
        if (closeBtn) {
            closeBtn.addEventListener('click', (e) => {
                e.preventDefault();
                e.stopPropagation();
                this.forceClose();
            });
        }
        
        // Navigation buttons
        const prevBtn = this.modalElement.querySelector('#prevImageBtn');
        const nextBtn = this.modalElement.querySelector('#nextImageBtn');
        
        if (prevBtn) {
            prevBtn.addEventListener('click', (e) => {
                e.stopPropagation();
                this.previousImage();
            });
        }
        
        if (nextBtn) {
            nextBtn.addEventListener('click', (e) => {
                e.stopPropagation();
                this.nextImage();
            });
        }
        
        // Gallery thumbnail clicks
        const galleryThumbs = document.querySelectorAll('.gallery-thumbnail');
        galleryThumbs.forEach((thumb, index) => {
            thumb.addEventListener('click', (e) => {
                e.preventDefault();
                e.stopPropagation();
                this.showImage(index);
                this.open(index);
            });
        });

        // Modal thumbnail clicks
        const modalThumbs = this.modalElement.querySelectorAll('.modal-thumb');
        modalThumbs.forEach((thumb, index) => {
            thumb.addEventListener('click', (e) => {
                e.stopPropagation();
                this.showImage(index);
            });
        });
        
        // Keyboard navigation
        document.addEventListener('keydown', (e) => {
            if (this.isModalOpen()) {
                switch(e.key) {
                    case 'Escape':
                        e.preventDefault();
                        this.forceClose();
                        break;
                    case 'ArrowLeft':
                        e.preventDefault();
                        this.previousImage();
                        break;
                    case 'ArrowRight':
                        e.preventDefault();
                        this.nextImage();
                        break;
                }
            }
        });
        
        // Backdrop click
        this.modalElement.addEventListener('click', (e) => {
            if (e.target === this.modalElement) {
                this.forceClose();
            }
        });
        
        // Modal events
        this.modalElement.addEventListener('hidden.bs.modal', () => {
            this.cleanup();
        });
        
        this.modalElement.addEventListener('show.bs.modal', () => {
            // Ensure proper z-index
            this.modalElement.style.zIndex = '1055';
        });
    }
    
    addStyles() {
        if (document.getElementById('stable-modal-styles')) return;
        
        const style = document.createElement('style');
        style.id = 'stable-modal-styles';
        style.textContent = `
            .modal-thumb {
                transition: all 0.3s ease;
                border: 2px solid transparent;
            }

            .modal-thumb:hover {
                transform: scale(1.1);
                border-color: #0d6efd;
            }

            .modal-thumb.active {
                border-color: #0d6efd !important;
                transform: scale(1.1) !important;
            }

            .gallery-thumbnail {
                transition: all 0.3s ease;
                border: 2px solid transparent;
            }

            .gallery-thumbnail:hover {
                transform: scale(1.05);
                border-color: #0d6efd;
            }

            .active-thumbnail .gallery-thumbnail {
                border-color: #0d6efd !important;
                transform: scale(1.05) !important;
            }

            #modalImage {
                transition: opacity 0.3s ease;
                max-height: 80vh;
                object-fit: contain;
            }

            .modal-backdrop {
                z-index: 1050;
            }

            .modal {
                z-index: 1055;
            }
        `;
        document.head.appendChild(style);
    }
    
    open(index = 0) {
        this.currentIndex = index;
        
        // Clean up any existing modal
        this.cleanup();
        
        // Create new modal instance
        this.modalInstance = new bootstrap.Modal(this.modalElement, {
            backdrop: true,
            keyboard: true,
            focus: true
        });
        
        // Show image and modal
        this.showImage(index);
        this.modalInstance.show();
    }
    
    showImage(index) {
        if (index < 0 || index >= this.images.length) return;
        
        this.currentIndex = index;
        const image = this.images[index];
        
        // Update modal image
        const modalImage = this.modalElement.querySelector('#modalImage');
        if (modalImage && image) {
            modalImage.style.opacity = '0.5';
            
            const newImg = new Image();
            newImg.onload = () => {
                modalImage.src = image.src;
                modalImage.alt = image.alt;
                modalImage.style.opacity = '1';

                // ปรับขนาดรูปภาพตามอัตราส่วน
                this.adjustImageSize(modalImage, newImg);
            };
            newImg.onerror = () => {
                console.error('Failed to load image:', image.src);
                modalImage.style.opacity = '1';
            };
            newImg.src = image.src;
        }
        
        // Update active thumbnail
        this.updateActiveThumbnail(index);
    }
    
    updateActiveThumbnail(activeIndex) {
        // Update gallery thumbnails
        const galleryThumbs = document.querySelectorAll('.gallery-thumbnail');
        galleryThumbs.forEach((thumb, index) => {
            const container = thumb.parentElement;
            if (index === activeIndex) {
                container.classList.add('active-thumbnail');
                thumb.style.borderColor = '#0d6efd';
            } else {
                container.classList.remove('active-thumbnail');
                thumb.style.borderColor = 'transparent';
            }
        });

        // Update modal thumbnails
        const modalThumbs = this.modalElement.querySelectorAll('.modal-thumb');
        modalThumbs.forEach((thumb, index) => {
            if (index === activeIndex) {
                thumb.classList.add('active');
                thumb.style.borderColor = '#0d6efd';
            } else {
                thumb.classList.remove('active');
                thumb.style.borderColor = 'transparent';
            }
        });
    }
    
    nextImage() {
        const nextIndex = (this.currentIndex + 1) % this.images.length;
        this.showImage(nextIndex);
    }
    
    previousImage() {
        const prevIndex = (this.currentIndex - 1 + this.images.length) % this.images.length;
        this.showImage(prevIndex);
    }
    
    forceClose() {
        if (this.modalInstance) {
            this.modalInstance.hide();
        } else {
            // Force close if no instance
            this.modalElement.classList.remove('show');
            this.modalElement.style.display = 'none';
            this.cleanup();
        }
    }
    
    cleanup() {
        // Remove modal instance
        if (this.modalInstance) {
            this.modalInstance.dispose();
            this.modalInstance = null;
        }
        
        // Clean up body
        document.body.classList.remove('modal-open');
        document.body.style.removeProperty('padding-right');
        document.body.style.removeProperty('overflow');
        
        // Remove all backdrops
        const backdrops = document.querySelectorAll('.modal-backdrop');
        backdrops.forEach(backdrop => backdrop.remove());
        
        // Reset modal element
        this.modalElement.style.display = 'none';
        this.modalElement.setAttribute('aria-hidden', 'true');
        this.modalElement.removeAttribute('aria-modal');
        this.modalElement.style.removeProperty('z-index');
    }
    
    isModalOpen() {
        return this.modalElement.classList.contains('show');
    }

    // ปรับขนาดรูปภาพตามอัตราส่วน
    adjustImageSize(modalImage, loadedImage) {
        if (!modalImage || !loadedImage) return;

        const imageWidth = loadedImage.naturalWidth || loadedImage.width;
        const imageHeight = loadedImage.naturalHeight || loadedImage.height;

        if (imageWidth === 0 || imageHeight === 0) return;

        // คำนวณอัตราส่วน
        const aspectRatio = imageWidth / imageHeight;

        // ลบ class เก่าทั้งหมด
        modalImage.classList.remove(
            'modal-image-landscape',
            'modal-image-portrait',
            'modal-image-square',
            'modal-image-wide',
            'modal-image-tall'
        );

        // กำหนด class ใหม่ตามอัตราส่วน
        if (aspectRatio > 2.5) {
            // รูปภาพกว้างมากๆ (Panorama)
            modalImage.classList.add('modal-image-wide');
        } else if (aspectRatio > 1.3) {
            // รูปภาพแนวนอน (Landscape)
            modalImage.classList.add('modal-image-landscape');
        } else if (aspectRatio > 0.8) {
            // รูปภาพสี่เหลี่ยมจัตุรัส
            modalImage.classList.add('modal-image-square');
        } else if (aspectRatio > 0.4) {
            // รูปภาพแนวตั้ง (Portrait)
            modalImage.classList.add('modal-image-portrait');
        } else {
            // รูปภาพสูงมากๆ
            modalImage.classList.add('modal-image-tall');
        }

        // ปรับขนาดเพิ่มเติมสำหรับรูปภาพขนาดใหญ่มาก
        if (imageWidth > 3000 || imageHeight > 3000) {
            modalImage.style.maxWidth = '80vw';
            modalImage.style.maxHeight = '70vh';
        } else if (imageWidth > 2000 || imageHeight > 2000) {
            modalImage.style.maxWidth = '85vw';
            modalImage.style.maxHeight = '75vh';
        }

        // ปรับสำหรับหน้าจอมือถือ
        if (window.innerWidth <= 768) {
            if (aspectRatio > 2) {
                modalImage.style.maxWidth = '95vw';
                modalImage.style.maxHeight = '45vh';
            } else if (aspectRatio < 0.6) {
                modalImage.style.maxWidth = '70vw';
                modalImage.style.maxHeight = '70vh';
            }
        }
    }
}

// Global functions for backward compatibility
window.StableModalGallery = StableModalGallery;

// Auto-initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    if (document.getElementById('imageModal')) {
        window.modalGallery = new StableModalGallery('imageModal');
        
        // Global function for opening modal
        window.openImageModal = function(index = 0) {
            if (window.modalGallery) {
                window.modalGallery.open(index);
            }
        };
        
        // Global function for closing modal
        window.closeImageModal = function() {
            if (window.modalGallery) {
                window.modalGallery.forceClose();
            }
        };
    }
});

// Force cleanup on page unload
window.addEventListener('beforeunload', function() {
    if (window.modalGallery) {
        window.modalGallery.cleanup();
    }
});
