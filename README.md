# ผู้ใหญ่ประจักษ์ เซอร์วิส ช็อป (Phuyai Prajak Service Shop)

<p align="center">
    <img src="https://via.placeholder.com/400x100/2563eb/ffffff?text=Phuyai+Prajak+Service+Shop" alt="Phuyai Prajak Service Shop" width="400">
</p>

<p align="center">
    <strong>ผู้เชี่ยวชาญด้านการให้บริการที่ครบครันและมีคุณภาพ</strong><br>
    <em>บริการด้วยใจ เพื่อความพึงพอใจของลูกค้า</em>
</p>

## เกี่ยวกับเรา

ผู้ใหญ่ประจักษ์ เซอร์วิส ช็อป เป็นระบบจัดการเว็บไซต์สำหรับธุรกิจให้บริการ ที่พัฒนาด้วย Laravel Framework เพื่อให้การจัดการข้อมูลและการแสดงผลเป็นไปอย่างมีประสิทธิภาพ

## คุณสมบัติหลัก

- 🏠 **หน้าแรก** - แสดงข้อมูลบริษัทและบริการหลัก
- 📋 **จัดการกิจกรรม** - ระบบจัดการกิจกรรมและภาพประกอบ
- 👥 **ระบบผู้ดูแล** - ระบบจัดการเนื้อหาสำหรับผู้ดูแลระบบ
- 📱 **Responsive Design** - รองรับการใช้งานบนอุปกรณ์ทุกขนาด
- 🔒 **ระบบความปลอดภัย** - ระบบยืนยันตัวตนและการจัดการสิทธิ์

## เทคโนโลยีที่ใช้

- **Backend**: Laravel 9.x
- **Frontend**: Blade Templates, Bootstrap 5
- **Database**: MySQL
- **Server**: Apache (XAMPP)
- **PHP**: 8.0+

## การติดตั้ง

### ความต้องการของระบบ
- PHP >= 8.0
- Composer
- MySQL
- Apache/Nginx
- Node.js & NPM (สำหรับ frontend assets)

### ขั้นตอนการติดตั้ง

1. **Clone โปรเจกต์**
   ```bash
   git clone [repository-url]
   cd "Phuyai Prajak service shop"
   ```

2. **ติดตั้ง Dependencies**
   ```bash
   composer install
   npm install
   ```

3. **ตั้งค่าไฟล์ Environment**
   ```bash
   cp .env.example .env
   php artisan key:generate
   ```

4. **ตั้งค่าฐานข้อมูล**
   - สร้างฐานข้อมูล `phuyai_prajak_service_shop`
   - แก้ไขไฟล์ `.env` ให้ตรงกับการตั้งค่าฐานข้อมูล

5. **รัน Migration และ Seeder**
   ```bash
   php artisan migrate
   php artisan db:seed
   ```

6. **สร้าง Storage Link**
   ```bash
   php artisan storage:link
   ```

7. **Compile Assets**
   ```bash
   npm run dev
   # หรือสำหรับ production
   npm run production
   ```

## การใช้งาน

### เริ่มต้นใช้งาน
```bash
php artisan serve
```

เข้าใช้งานที่: `http://localhost:8000`

### ข้อมูลผู้ดูแลระบบเริ่มต้น
- **Email**: <EMAIL>
- **Password**: 123456789

## โครงสร้างโปรเจกต์

```
Phuyai Prajak service shop/
├── app/
│   ├── Http/Controllers/     # Controllers
│   ├── Models/              # Models
│   └── ...
├── database/
│   ├── migrations/          # Database migrations
│   └── seeders/            # Database seeders
├── public/
│   ├── css/                # Compiled CSS
│   ├── js/                 # Compiled JavaScript
│   └── storage/            # Public storage
├── resources/
│   ├── views/              # Blade templates
│   ├── css/                # Source CSS
│   └── js/                 # Source JavaScript
└── ...
```

## การพัฒนา

### การเพิ่มฟีเจอร์ใหม่
1. สร้าง Migration สำหรับฐานข้อมูล
2. สร้าง Model และ Controller
3. เพิ่ม Routes ใน `routes/web.php`
4. สร้าง Views ใน `resources/views/`

### การทดสอบ
```bash
php artisan test
```

## การติดต่อ

- **เว็บไซต์**: [เว็บไซต์ของเรา]
- **อีเมล**: <EMAIL>
- **โทรศัพท์**: 02-123-4567
- **ที่อยู่**: 123 ถนนสุขุมวิท แขวงคลองเตย เขตคลองเตย กรุงเทพฯ 10110

## License

โปรเจกต์นี้ใช้ [MIT License](https://opensource.org/licenses/MIT)
