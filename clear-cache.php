<?php
/**
 * <PERSON><PERSON> Cache Clearing Script
 * ใช้สำหรับล้าง cache เมื่อแก้ไขข้อมูลในฐานข้อมูลโดยตรง
 */

echo "========================================\n";
echo "   Laravel Cache Clearing Script\n";
echo "========================================\n\n";

// ตรวจสอบว่าอยู่ในโฟลเดอร์ Laravel หรือไม่
if (!file_exists('artisan')) {
    echo "❌ Error: ไม่พบไฟล์ artisan กรุณาเรียกใช้ script นี้ในโฟลเดอร์ root ของ Laravel\n";
    exit(1);
}

$commands = [
    'cache:clear' => 'Clearing application cache',
    'view:clear' => 'Clearing compiled views',
    'config:clear' => 'Clearing configuration cache',
    'route:clear' => 'Clearing route cache'
];

$step = 1;
$total = count($commands);

foreach ($commands as $command => $description) {
    echo "[{$step}/{$total}] {$description}...\n";
    
    $output = [];
    $return_code = 0;
    exec("php artisan {$command} 2>&1", $output, $return_code);
    
    if ($return_code === 0) {
        echo "✅ Success: " . implode("\n", $output) . "\n\n";
    } else {
        echo "❌ Error: " . implode("\n", $output) . "\n\n";
    }
    
    $step++;
}

echo "========================================\n";
echo "   All caches cleared successfully!\n";
echo "========================================\n\n";
echo "🔄 กรุณารีเฟรชเบราว์เซอร์เพื่อดูการเปลี่ยนแปลง\n\n";

// แสดงข้อมูลเพิ่มเติม
echo "📝 หมายเหตุ:\n";
echo "- หากยังไม่เห็นการเปลี่ยนแปลง ให้กด Ctrl+F5 (Hard Refresh)\n";
echo "- หากใช้ Incognito/Private Mode อาจช่วยได้\n";
echo "- ตรวจสอบว่าแก้ไขฐานข้อมูลถูกต้องแล้ว\n\n";
?>
