<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\ServiceCategory;

class ServiceCategorySeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $categories = [
            [
                'name' => 'ดอกไม้สด',
                'description' => 'บริการจัดดอกไม้สดสำหรับงานศพ พวงหรีด และการตกแต่ง',
                'icon' => 'fas fa-seedling',
                'color' => '#28a745',
                'is_active' => true,
                'sort_order' => 1
            ],
            [
                'name' => 'ดอกไม้แห้ง',
                'description' => 'บริการจัดดอกไม้แห้งที่คงทนและสวยงาม',
                'icon' => 'fas fa-leaf',
                'color' => '#6f42c1',
                'is_active' => true,
                'sort_order' => 2
            ],
            [
                'name' => 'โลงศพ',
                'description' => 'โลงศพคุณภาพดีหลากหลายแบบและราคา',
                'icon' => 'fas fa-box',
                'color' => '#6c757d',
                'is_active' => true,
                'sort_order' => 3
            ],
            [
                'name' => 'เครื่องสักการะ',
                'description' => 'เครื่องสักการะและอุปกรณ์ทางศาสนา',
                'icon' => 'fas fa-praying-hands',
                'color' => '#fd7e14',
                'is_active' => true,
                'sort_order' => 4
            ],
            [
                'name' => 'บริการรับจัดงาน',
                'description' => 'บริการรับจัดงานศพแบบครบวงจร',
                'icon' => 'fas fa-hands-helping',
                'color' => '#007bff',
                'is_active' => true,
                'sort_order' => 5
            ],
            [
                'name' => 'บริการขนส่ง',
                'description' => 'บริการขนส่งศพและอุปกรณ์งานศพ',
                'icon' => 'fas fa-truck',
                'color' => '#dc3545',
                'is_active' => true,
                'sort_order' => 6
            ]
        ];

        foreach ($categories as $category) {
            ServiceCategory::create($category);
        }
    }
}
