<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>สร้างรูปภาพตัวอย่างแบนเนอร์</title>
    <style>
        body {
            font-family: 'Kanit', sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        .banner {
            width: 1200px;
            height: 400px;
            margin: 20px 0;
            position: relative;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
        .banner1 {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .banner2 {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        }
        .banner3 {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }
        .banner-content {
            position: absolute;
            top: 50%;
            left: 50px;
            transform: translateY(-50%);
            color: white;
            z-index: 2;
        }
        .banner h1 {
            font-size: 3rem;
            margin: 0 0 20px 0;
            font-weight: 700;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        .banner p {
            font-size: 1.5rem;
            margin: 0;
            opacity: 0.9;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
        }
        .banner::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.2);
            z-index: 1;
        }
        .download-btn {
            background: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 10px 0;
            font-size: 16px;
        }
        .download-btn:hover {
            background: #0056b3;
        }
    </style>
    <link href="https://fonts.googleapis.com/css2?family=Kanit:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <h1>สร้างรูปภาพตัวอย่างแบนเนอร์</h1>
    <p>คลิกขวาที่รูปภาพแล้วเลือก "Save image as..." เพื่อบันทึกรูปภาพ</p>

    <div class="banner banner1" id="banner1">
        <div class="banner-content">
            <h1>ผู้ใหญ่ประจักษ์ เซอร์วิส ช็อป</h1>
            <p>บริการที่ดีที่สุด ด้วยความใส่ใจและคุณภาพ</p>
        </div>
    </div>
    <button class="download-btn" onclick="downloadBanner('banner1', 'sample-banner-1.jpg')">ดาวน์โหลดแบนเนอร์ 1</button>

    <div class="banner banner2" id="banner2">
        <div class="banner-content">
            <h1>โปรโมชั่นพิเศษ</h1>
            <p>สำหรับลูกค้าใหม่ ลดราคาพิเศษ</p>
        </div>
    </div>
    <button class="download-btn" onclick="downloadBanner('banner2', 'sample-banner-2.jpg')">ดาวน์โหลดแบนเนอร์ 2</button>

    <div class="banner banner3" id="banner3">
        <div class="banner-content">
            <h1>ติดต่อเรา</h1>
            <p>บริการตลอด 24 ชั่วโมง ทุกวัน</p>
        </div>
    </div>
    <button class="download-btn" onclick="downloadBanner('banner3', 'sample-banner-3.jpg')">ดาวน์โหลดแบนเนอร์ 3</button>

    <script src="https://html2canvas.hertzen.com/dist/html2canvas.min.js"></script>
    <script>
        function downloadBanner(elementId, filename) {
            const element = document.getElementById(elementId);
            html2canvas(element, {
                width: 1200,
                height: 400,
                scale: 2
            }).then(canvas => {
                const link = document.createElement('a');
                link.download = filename;
                link.href = canvas.toDataURL('image/jpeg', 0.9);
                link.click();
            });
        }
    </script>
</body>
</html>
