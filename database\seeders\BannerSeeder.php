<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class BannerSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $banners = [
            [
                'title' => 'แบนเนอร์หลัก - บริการครบครัน',
                'description' => 'บริการที่ดีที่สุด ด้วยความใส่ใจและคุณภาพ',
                'image_path' => 'banners/sample-banner-1.jpg',
                'link_url' => null,
                'is_active' => true,
                'sort_order' => 1,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'title' => 'แบนเนอร์โปรโมชั่น',
                'description' => 'โปรโมชั่นพิเศษสำหรับลูกค้าใหม่',
                'image_path' => 'banners/sample-banner-2.jpg',
                'link_url' => route('packages'),
                'is_active' => true,
                'sort_order' => 2,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'title' => 'แบนเนอร์ติดต่อเรา',
                'description' => 'ติดต่อเราได้ตลอด 24 ชั่วโมง',
                'image_path' => 'banners/sample-banner-3.jpg',
                'link_url' => route('contact'),
                'is_active' => true,
                'sort_order' => 3,
                'created_at' => now(),
                'updated_at' => now(),
            ],
        ];

        \DB::table('banners')->insert($banners);
    }
}
