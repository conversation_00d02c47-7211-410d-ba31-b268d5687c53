<?php
/**
 * ลบตาราง service_categories อย่างรวดเร็ว
 */

require_once __DIR__ . '/vendor/autoload.php';

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

// Bootstrap Laravel
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

echo "========================================\n";
echo "   ลบตาราง service_categories\n";
echo "========================================\n\n";

try {
    // ตรวจสอบการเชื่อมต่อฐานข้อมูล
    DB::connection()->getPdo();
    echo "✅ การเชื่อมต่อฐานข้อมูล: สำเร็จ\n\n";

    // ตรวจสอบตาราง
    if (!Schema::hasTable('service_categories')) {
        echo "❌ ไม่พบตาราง service_categories\n";
        exit(0);
    }

    $categoryCount = DB::table('service_categories')->count();
    echo "📊 จำนวนหมวดหมู่: {$categoryCount} รายการ\n";

    // ตรวจสอบบริการที่อ้างอิง
    $servicesWithCategory = DB::table('services')->whereNotNull('category_id')->count();
    echo "📋 บริการที่อ้างอิงหมวดหมู่: {$servicesWithCategory} รายการ\n\n";

    echo "🚀 เริ่มลบตาราง service_categories...\n";

    // 1. ลบการอ้างอิงในตาราง services
    if ($servicesWithCategory > 0) {
        echo "1. ลบการอ้างอิงหมวดหมู่ในตาราง services...\n";
        DB::table('services')->whereNotNull('category_id')->update(['category_id' => null]);
        echo "✅ ลบการอ้างอิงแล้ว\n";
    }

    // 2. ลบตาราง service_categories
    echo "2. ลบตาราง service_categories...\n";
    Schema::dropIfExists('service_categories');
    echo "✅ ลบตารางสำเร็จ\n";

    // 3. ลบ column category_id จากตาราง services
    echo "3. ลบ column category_id จากตาราง services...\n";
    if (Schema::hasColumn('services', 'category_id')) {
        Schema::table('services', function ($table) {
            $table->dropColumn('category_id');
        });
        echo "✅ ลบ column สำเร็จ\n";
    }

    echo "\n🎉 ลบตาราง service_categories เสร็จสิ้น!\n";
    echo "----------------------------------------\n";
    echo "✅ ลบตาราง service_categories\n";
    echo "✅ ลบการอ้างอิงใน services\n";
    echo "✅ ลบ column category_id\n\n";

    // แสดงตารางที่เหลือ
    $databaseName = config('database.connections.mysql.database');
    $tables = DB::select("SHOW TABLES");
    $tableColumn = "Tables_in_{$databaseName}";
    
    echo "📋 ตารางที่เหลือ: " . count($tables) . " ตาราง\n";
    foreach ($tables as $table) {
        $tableName = $table->$tableColumn;
        $count = DB::table($tableName)->count();
        echo "   - {$tableName} ({$count} รายการ)\n";
    }

} catch (Exception $e) {
    echo "❌ เกิดข้อผิดพลาด: " . $e->getMessage() . "\n";
}

echo "\n========================================\n";
echo "   เสร็จสิ้น\n";
echo "========================================\n";
?>
