@if ($paginator->hasPages())
<div class="d-flex justify-content-between align-items-center flex-wrap gap-3">
    <!-- Pagination Info -->
    <div class="pagination-info">
        <i class="fas fa-info-circle me-1"></i>
        แสดง <strong>{{ $paginator->firstItem() ?? 0 }}</strong> ถึง <strong>{{ $paginator->lastItem() ?? 0 }}</strong>
        จากทั้งหมด <strong>{{ $paginator->total() }}</strong> รายการ
    </div>

    <!-- Pagination Links -->
    <nav aria-label="Pagination">
        <ul class="pagination mb-0">
        {{-- Previous Page Link --}}
        @if ($paginator->onFirstPage())
            <li class="page-item disabled">
                <span class="page-link">
                    <i class="fas fa-chevron-left"></i>
                </span>
            </li>
        @else
            <li class="page-item">
                <a class="page-link" href="{{ $paginator->previousPageUrl() }}" rel="prev">
                    <i class="fas fa-chevron-left"></i>
                </a>
            </li>
        @endif

        {{-- Pagination Elements --}}
        @php
            $start = max(1, $paginator->currentPage() - 2);
            $end = min($paginator->lastPage(), $paginator->currentPage() + 2);
        @endphp

        {{-- First Page --}}
        @if ($start > 1)
            <li class="page-item">
                <a class="page-link" href="{{ $paginator->url(1) }}">1</a>
            </li>
            @if ($start > 2)
                <li class="page-item disabled">
                    <span class="page-link">...</span>
                </li>
            @endif
        @endif

        {{-- Page Numbers --}}
        @for ($page = $start; $page <= $end; $page++)
            @if ($page == $paginator->currentPage())
                <li class="page-item active">
                    <span class="page-link">{{ $page }}</span>
                </li>
            @else
                <li class="page-item">
                    <a class="page-link" href="{{ $paginator->url($page) }}">{{ $page }}</a>
                </li>
            @endif
        @endfor

        {{-- Last Page --}}
        @if ($end < $paginator->lastPage())
            @if ($end < $paginator->lastPage() - 1)
                <li class="page-item disabled">
                    <span class="page-link">...</span>
                </li>
            @endif
            <li class="page-item">
                <a class="page-link" href="{{ $paginator->url($paginator->lastPage()) }}">{{ $paginator->lastPage() }}</a>
            </li>
        @endif

        {{-- Next Page Link --}}
        @if ($paginator->hasMorePages())
            <li class="page-item">
                <a class="page-link" href="{{ $paginator->nextPageUrl() }}" rel="next">
                    <i class="fas fa-chevron-right"></i>
                </a>
            </li>
        @else
            <li class="page-item disabled">
                <span class="page-link">
                    <i class="fas fa-chevron-right"></i>
                </span>
            </li>
        @endif
        </ul>
    </nav>

    <!-- Page Jump -->
    <div class="pagination-jump d-flex align-items-center">
        <span class="text-muted small me-2">
            <i class="fas fa-external-link-alt me-1"></i>ไปหน้า:
        </span>
        <select class="form-select form-select-sm" onchange="goToPage(this.value)">
            @for ($i = 1; $i <= $paginator->lastPage(); $i++)
                <option value="{{ $paginator->url($i) }}" {{ $i == $paginator->currentPage() ? 'selected' : '' }}>
                    {{ $i }}
                </option>
            @endfor
        </select>
    </div>
</div>



<script>
function goToPage(url) {
    if (url) {
        // Add loading state
        const select = event.target;
        const originalText = select.options[select.selectedIndex].text;
        select.disabled = true;

        // Show loading overlay
        const overlay = document.createElement('div');
        overlay.className = 'loading-overlay show';
        overlay.innerHTML = `
            <div class="text-center">
                <div class="loading-spinner"></div>
                <p class="mt-3 text-muted">กำลังโหลดหน้า ${originalText}...</p>
            </div>
        `;
        document.body.appendChild(overlay);

        // Navigate to page
        window.location.href = url;
    }
}

// Add smooth transitions to pagination links
document.addEventListener('DOMContentLoaded', function() {
    const paginationLinks = document.querySelectorAll('.pagination .page-link');

    paginationLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            if (!this.closest('.page-item').classList.contains('disabled') &&
                !this.closest('.page-item').classList.contains('active')) {

                // Add loading state
                this.style.pointerEvents = 'none';
                this.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';

                // Show page loading
                setTimeout(() => {
                    const overlay = document.createElement('div');
                    overlay.className = 'loading-overlay show';
                    overlay.innerHTML = `
                        <div class="text-center">
                            <div class="loading-spinner"></div>
                            <p class="mt-3 text-muted">กำลังโหลดหน้าถัดไป...</p>
                        </div>
                    `;
                    document.body.appendChild(overlay);
                }, 100);
            }
        });
    });
});
</script>
@endif
