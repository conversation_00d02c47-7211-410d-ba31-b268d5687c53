# 🎯 คู่มือการใช้งานฐานข้อมูล - ระบบบริการจัดงานศพ

## 📋 สรุปการใช้งานแต่ละตาราง

### 🏠 **หน้าเว็บไซต์ที่ใช้ข้อมูล**

#### 🏡 **หน้าหลัก (Home)**
```
📊 ข้อมูลที่แสดง:
├── banners (สไลด์โชว์ด้านบน)
├── services (6 บริการแนะนำ)
├── activities (4 ผลงานแบบสุ่ม)
└── site_settings (ข้อมูลติดต่อ)
```

#### 🛠️ **หน้าบริการ (Services)**
```
📊 ข้อมูลที่แสดง:
├── banners (สไลด์โชว์ด้านบน)
├── services (บริการทั้งหมด แบ่งหน้า 9 รายการ)
├── service_images (รูปภาพของแต่ละบริการ)
└── site_settings (ข้อมูลติดต่อ)
```

#### 📦 **หน้าแพ็คเกจ (Packages)**
```
📊 ข้อมูลที่แสดง:
├── packages (แพ็คเกจทั้งหมด)
└── site_settings (ข้อมูลติดต่อ)
```

#### 🎨 **หน้าผลงาน (Activities)**
```
📊 ข้อมูลที่แสดง:
├── activities (ผลงานทั้งหมด แบ่งหน้า 9 รายการ)
├── activity_images (รูปภาพของแต่ละผลงาน)
└── site_settings (ข้อมูลติดต่อ)
```

#### 📞 **หน้าติดต่อ (Contact)**
```
📊 ข้อมูลที่แสดง:
├── site_settings (ข้อมูลติดต่อ, แผนที่)
└── contacts (บันทึกข้อความจากลูกค้า)
```

---

## 🔧 **ระบบ Admin - การจัดการข้อมูล**

### 👤 **การเข้าสู่ระบบ**
```
ตาราง: users
├── ตรวจสอบ email + password
├── สร้าง session
└── เข้าสู่หน้า Dashboard
```

### 📊 **หน้า Dashboard**
```
📈 สถิติที่แสดง:
├── จำนวน services ทั้งหมด
├── จำนวน packages ทั้งหมด  
├── จำนวน activities ทั้งหมด
├── จำนวน contacts ทั้งหมด
├── จำนวน contacts ที่ยังไม่อ่าน
└── contacts ล่าสุด 3 รายการ
```

### 🛠️ **จัดการบริการ (Services)**
```
🔧 ฟีเจอร์:
├── ดูรายการบริการทั้งหมด (services)
├── เพิ่ม/แก้ไข/ลบ บริการ
├── อัปโหลดรูปภาพหลัก
├── จัดการแกลเลอรี่ (service_images)
├── กำหนดรูปหน้าปก (is_cover)
└── เรียงลำดับการแสดงผล (sort_order)
```

### 📦 **จัดการแพ็คเกจ (Packages)**
```
🔧 ฟีเจอร์:
├── ดูรายการแพ็คเกจทั้งหมด
├── เพิ่ม/แก้ไข/ลบ แพ็คเกจ
├── กำหนดราคา (price)
├── อัปโหลดรูปภาพ
└── เปิด/ปิด การแสดงผล (is_active)
```

### 🎨 **จัดการผลงาน (Activities)**
```
🔧 ฟีเจอร์:
├── ดูรายการผลงานทั้งหมด (activities)
├── เพิ่ม/แก้ไข/ลบ ผลงาน
├── อัปโหลดรูปภาพหลัก
├── จัดการแกลเลอรี่ (activity_images)
├── กำหนดรูปหน้าปก (is_cover)
└── เรียงลำดับการแสดงผล (sort_order)
```

### 📞 **จัดการข้อความติดต่อ (Contacts)**
```
🔧 ฟีเจอร์:
├── ดูรายการข้อความทั้งหมด
├── อ่านรายละเอียดข้อความ
├── ทำเครื่องหมายอ่านแล้ว (is_read)
├── ลบข้อความ
├── ตอบกลับทางอีเมล (ลิงก์ mailto)
└── โทรหาลูกค้า (ลิงก์ tel)
```

### 🎨 **จัดการแบนเนอร์ (Banners)**
```
🔧 ฟีเจอร์:
├── ดูรายการแบนเนอร์ทั้งหมด
├── เพิ่ม/แก้ไข/ลบ แบนเนอร์
├── อัปโหลดรูปภาพ
├── เลือกหน้าที่ต้องการแสดง (display_pages)
├── เรียงลำดับการแสดงผล (sort_order)
└── เปิด/ปิด การแสดงผล (is_active)
```

### ⚙️ **การตั้งค่าเว็บไซต์ (Site Settings)**
```
🔧 การตั้งค่าที่มี:
├── site_name (ชื่อเว็บไซต์)
├── phone (เบอร์โทรศัพท์)
├── email (อีเมลติดต่อ)
├── address (ที่อยู่)
├── facebook (ลิงก์ Facebook)
├── line (ไอดี Line)
└── description (คำอธิบายเว็บไซต์)
```

---

## 💾 **ตัวอย่างข้อมูลจริงในฐานข้อมูล**

### 👤 **ตาราง users**
```sql
id: 1
name: "Admin"
email: "<EMAIL>"
password: "$2y$10$..." (เข้ารหัสแล้ว)
created_at: "2025-07-20 10:00:00"
```

### 🛠️ **ตาราง services**
```sql
id: 1
title: "บริการจัดงานศพแบบครบวงจร"
description: "บริการจัดงานศพที่ครอบคลุมทุกขั้นตอน"
details: "รวมถึงการจัดหาโลงศพ การแต่งหน้า..."
image: "services/service1.jpg"
is_active: 1
sort_order: 1
```

### 🖼️ **ตาราง service_images**
```sql
id: 1
service_id: 1
image_path: "services/service1_gallery1.jpg"
alt_text: "บริการจัดงานศพ"
description: "รูปภาพบริการจัดงานศพ"
is_cover: 1
sort_order: 1
```

### 📦 **ตาราง packages**
```sql
id: 1
title: "แพ็คเกจ A - ประหยัด"
description: "แพ็คเกจสำหรับงานศพขนาดเล็ก"
price: "15,000 - 25,000 บาท"
image: "packages/package1.jpg"
is_active: 1
```

### 🎨 **ตาราง activities**
```sql
id: 1
title: "งานศพคุณสมชาย"
description: "งานศพที่จัดในเดือนมกราคม"
details: "งานศพที่ประสบความสำเร็จ..."
image: "activities/activity1.jpg"
is_active: 1
```

### 📞 **ตาราง contacts**
```sql
id: 1
name: "คุณสมหญิง"
email: "<EMAIL>"
phone: "************"
subject: "สอบถามราคาแพ็คเกจ"
message: "ต้องการสอบถามราคาแพ็คเกจ A"
is_read: 0
```

### 🎨 **ตาราง banners**
```sql
id: 1
title: "แบนเนอร์หน้าหลัก"
description: "แบนเนอร์สำหรับหน้าหลัก"
image_path: "banners/banner1.jpg"
display_pages: ["home", "services"]
is_active: 1
sort_order: 1
```

### ⚙️ **ตาราง site_settings**
```sql
id: 1, key: "site_name", value: "บริการจัดงานศพ"
id: 2, key: "phone", value: "02-123-4567"
id: 3, key: "email", value: "<EMAIL>"
id: 4, key: "address", value: "123 ถนนสุขุมวิท กรุงเทพฯ"
id: 5, key: "facebook", value: "https://facebook.com/..."
id: 6, key: "line", value: "@lineofficial"
id: 7, key: "description", value: "บริการจัดงานศพครบวงจร"
```

---

## 🔄 **ขั้นตอนการทำงานของระบบ**

### 📝 **เมื่อลูกค้าเข้าเว็บไซต์:**
1. **หน้าหลัก** → ดึงข้อมูลจาก `banners`, `services`, `activities`, `site_settings`
2. **หน้าบริการ** → ดึงข้อมูลจาก `services`, `service_images`
3. **หน้าแพ็คเกจ** → ดึงข้อมูลจาก `packages`
4. **หน้าผลงาน** → ดึงข้อมูลจาก `activities`, `activity_images`
5. **หน้าติดต่อ** → แสดงข้อมูลจาก `site_settings`, บันทึกลง `contacts`

### 🔧 **เมื่อ Admin เข้าระบบ:**
1. **Login** → ตรวจสอบจาก `users`
2. **Dashboard** → นับข้อมูลจากทุกตาราง
3. **จัดการข้อมูล** → CRUD operations บนตารางต่างๆ
4. **อัปโหลดรูป** → บันทึกลง `service_images` หรือ `activity_images`

---

## 🎯 **จุดเด่นของการออกแบบฐานข้อมูล**

### ✅ **ข้อดี:**
1. **แยกรูปภาพเป็นตารางต่างหาก** - จัดการได้หลายรูปต่อรายการ
2. **มี is_active ทุกตาราง** - เปิด/ปิดการแสดงผลได้
3. **มี sort_order** - ควบคุมลำดับการแสดงผลได้
4. **ใช้ JSON สำหรับ display_pages** - ยืดหยุ่นในการแสดงแบนเนอร์
5. **แยก site_settings** - แก้ไขข้อมูลเว็บไซต์ได้ง่าย

### 🔧 **แนวทางปรับปรุง:**
1. เพิ่ม **SEO fields** (meta_title, meta_description)
2. เพิ่ม **ระบบ Tags** สำหรับจัดหมวดหมู่
3. เพิ่ม **ระบบ Comments** สำหรับผลงาน
4. เพิ่ม **ระบบ Analytics** ติดตามการเข้าชม
5. เพิ่ม **ระบบ Backup** อัตโนมัติ

---

## 📚 **สรุป**

ฐานข้อมูลนี้ออกแบบมาสำหรับ**เว็บไซต์บริการจัดงานศพ**ที่มีฟีเจอร์:
- 🏠 **หน้าแสดงบริการ** และแพ็คเกจ
- 🎨 **แกลเลอรี่ผลงาน** การให้บริการ
- 📞 **ระบบติดต่อ** จากลูกค้า
- 🔧 **ระบบ Admin** จัดการข้อมูล
- 🎨 **ระบบแบนเนอร์** สไลด์โชว์

**การออกแบบเน้นความยืดหยุ่น** สามารถเพิ่มข้อมูลได้ง่าย และ**ควบคุมการแสดงผล**ได้อย่างละเอียด 🎯
