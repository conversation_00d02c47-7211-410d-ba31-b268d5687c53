<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('banners', function (Blueprint $table) {
            $table->dropColumn(['link_url', 'link_type', 'link_id']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('banners', function (Blueprint $table) {
            $table->string('link_url')->nullable();
            $table->enum('link_type', ['none', 'custom', 'services', 'packages', 'activities', 'contact'])->default('none');
            $table->unsignedBigInteger('link_id')->nullable();
        });
    }
};
