<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\User;
use App\Models\SiteSetting;
use App\Models\Service;
use App\Models\Package;
use App\Models\Activity;
use Illuminate\Support\Facades\Hash;

class AdminUserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        // Create admin user
        User::create([
            'name' => 'ผู้ดูแลระบบ',
            'email' => '<EMAIL>',
            'password' => Hash::make('123456789'),
            'email_verified_at' => now(),
        ]);

        // Create site settings
        $settings = [
            'site_name' => 'ผู้ใหญ่ประจักษ์ เซอร์วิส ช็อป',
            'site_description' => 'ผู้เชี่ยวชาญด้านการให้บริการที่ครบครันและมีคุณภาพ บริการด้วยใจ เพื่อความพึงพอใจของลูกค้า',
            'contact_phone' => '02-123-4567',
            'contact_email' => '<EMAIL>',
            'contact_address' => '123 ถนนสุขุมวิท แขวงคลองเตย เขตคลองเตย กรุงเทพฯ 10110',
            'facebook_url' => 'https://facebook.com/phuyaiprajak',
            'line_id' => '@phuyaiprajak',
        ];

        foreach ($settings as $key => $value) {
            SiteSetting::create([
                'key' => $key,
                'value' => $value,
                'type' => 'text'
            ]);
        }

        // Create sample services
        $services = [
            [
                'title' => 'บริการพัฒนาเว็บไซต์',
                'description' => 'พัฒนาเว็บไซต์ที่ทันสมัยและใช้งานง่าย',
                'details' => 'บริการพัฒนาเว็บไซต์แบบครบวงจร ตั้งแต่การออกแบบ พัฒนา จนถึงการดูแลรักษา',
                'is_active' => true,
                'sort_order' => 1
            ],
            [
                'title' => 'บริการออกแบบกราฟิก',
                'description' => 'ออกแบบกราฟิกสำหรับสื่อโฆษณาและการตลาด',
                'details' => 'บริการออกแบบโลโก้ โบรชัวร์ โปสเตอร์ และสื่อการตลาดต่างๆ',
                'is_active' => true,
                'sort_order' => 2
            ],
            [
                'title' => 'บริการ SEO',
                'description' => 'เพิ่มอันดับการค้นหาในเว็บไซต์ของคุณ',
                'details' => 'บริการปรับปรุงเว็บไซต์ให้ติดอันดับการค้นหาใน Google',
                'is_active' => true,
                'sort_order' => 3
            ]
        ];

        foreach ($services as $service) {
            Service::create($service);
        }

        // Create sample packages
        $packages = [
            [
                'name' => 'แพคเกจเริ่มต้น',
                'description' => 'เหมาะสำหรับธุรกิจขนาดเล็ก',
                'features' => "- เว็บไซต์ 5 หน้า\n- ระบบจัดการเนื้อหา\n- รองรับมือถือ\n- โดเมนฟรี 1 ปี",
                'duration' => 'ครั้งเดียว',
                'is_featured' => false,
                'is_active' => true,
                'sort_order' => 1
            ],
            [
                'name' => 'แพคเกจมาตรฐาน',
                'description' => 'เหมาะสำหรับธุรกิจขนาดกลาง',
                'features' => "- เว็บไซต์ 10 หน้า\n- ระบบจัดการเนื้อหา\n- รองรับมือถือ\n- โดเมนฟรี 1 ปี\n- SEO พื้นฐาน\n- ระบบติดต่อ",
                'duration' => 'ครั้งเดียว',
                'is_featured' => true,
                'is_active' => true,
                'sort_order' => 2
            ],
            [
                'name' => 'แพคเกจพรีเมียม',
                'description' => 'เหมาะสำหรับธุรกิจขนาดใหญ่',
                'features' => "- เว็บไซต์ไม่จำกัดหน้า\n- ระบบจัดการเนื้อหา\n- รองรับมือถือ\n- โดเมนฟรี 1 ปี\n- SEO ขั้นสูง\n- ระบบติดต่อ\n- ระบบสมาชิก\n- การดูแลรักษา 1 ปี",
                'duration' => 'ครั้งเดียว',
                'is_featured' => false,
                'is_active' => true,
                'sort_order' => 3
            ]
        ];

        foreach ($packages as $package) {
            Package::create($package);
        }

        // Create sample activities
        $activities = [
            [
                'title' => 'งานเปิดตัวเว็บไซต์ใหม่',
                'description' => 'งานเปิดตัวเว็บไซต์รูปแบบใหม่ของบริษัท',
                'details' => 'จัดงานเปิดตัวเว็บไซต์ใหม่ที่มีฟีเจอร์ครบครันและใช้งานง่าย พร้อมการสาธิตการใช้งานและรับฟังความคิดเห็นจากลูกค้า',
                'image' => 'activities/sample1.jpg',
                'activity_date' => now()->subDays(30),
                'location' => 'ห้องประชุมใหญ่ ชั้น 5',
                'is_active' => true,
                'sort_order' => 1
            ],
            [
                'title' => 'สัมมนาการตลาดออนไลน์',
                'description' => 'สัมมนาเรื่องกลยุทธ์การตลาดออนไลน์ในยุคดิจิทัล',
                'details' => 'การสัมมนาที่รวบรวมผู้เชี่ยวชาญด้านการตลาดออนไลน์มาแชร์ประสบการณ์และเทคนิคต่างๆ ที่จะช่วยให้ธุรกิจเติบโตในโลกดิจิทัล',
                'image' => 'activities/sample2.jpg',
                'activity_date' => now()->subDays(15),
                'location' => 'โรงแรมแกรนด์ ห้องบอลรูม',
                'is_active' => true,
                'sort_order' => 2
            ],
            [
                'title' => 'Workshop การออกแบบ UX/UI',
                'description' => 'เวิร์คช็อปการออกแบบประสบการณ์ผู้ใช้และส่วนติดต่อ',
                'details' => 'เวิร์คช็อปเชิงปฏิบัติการที่สอนหลักการออกแบบ UX/UI ตั้งแต่พื้นฐานจนถึงขั้นสูง พร้อมการฝึกปฏิบัติจริง',
                'image' => 'activities/sample3.jpg',
                'activity_date' => now()->subDays(7),
                'location' => 'ศูนย์การเรียนรู้ดิจิทัล',
                'is_active' => true,
                'sort_order' => 3
            ]
        ];

        foreach ($activities as $activity) {
            Activity::create($activity);
        }
    }
}
