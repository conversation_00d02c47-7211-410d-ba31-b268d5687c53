<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>E-R Diagram ระบบฐานข้อมูลเว็บไซต์ผู้ใหญ่ประจักษ์ เซอร์วิส ช็อป</title>
    <style>
        body {
            font-family: 'Sarabun', 'TH Sarabun New', Arial, sans-serif;
            line-height: 1.6;
            margin: 40px;
            color: #333;
            background-color: #f9f9f9;
        }
        
        .header {
            text-align: center;
            margin-bottom: 40px;
            padding: 30px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        
        .header h1 {
            font-size: 28px;
            margin: 0;
            font-weight: bold;
        }
        
        .header h2 {
            font-size: 20px;
            margin: 10px 0 0 0;
            font-weight: normal;
            opacity: 0.9;
        }
        
        .section {
            background: white;
            margin: 30px 0;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .section h3 {
            color: #667eea;
            font-size: 22px;
            margin-bottom: 20px;
            border-bottom: 3px solid #667eea;
            padding-bottom: 10px;
        }
        
        .table-container {
            overflow-x: auto;
            margin: 20px 0;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
        }
        
        th {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px 10px;
            text-align: left;
            font-weight: bold;
            font-size: 14px;
        }
        
        td {
            padding: 12px 10px;
            border-bottom: 1px solid #eee;
            font-size: 13px;
        }
        
        tr:nth-child(even) {
            background-color: #f8f9fa;
        }
        
        tr:hover {
            background-color: #e3f2fd;
        }
        
        .entity-name {
            background: #667eea;
            color: white;
            padding: 10px 15px;
            border-radius: 5px;
            font-weight: bold;
            margin: 20px 0 10px 0;
            display: inline-block;
        }
        
        .key-pk {
            background: #ff6b6b;
            color: white;
            padding: 3px 8px;
            border-radius: 3px;
            font-size: 11px;
            font-weight: bold;
        }
        
        .key-fk {
            background: #4ecdc4;
            color: white;
            padding: 3px 8px;
            border-radius: 3px;
            font-size: 11px;
            font-weight: bold;
        }
        
        .key-uk {
            background: #45b7d1;
            color: white;
            padding: 3px 8px;
            border-radius: 3px;
            font-size: 11px;
            font-weight: bold;
        }
        
        .relationship {
            background: #f8f9fa;
            border-left: 4px solid #667eea;
            padding: 15px;
            margin: 15px 0;
            border-radius: 0 5px 5px 0;
        }
        
        .usage {
            background: #e8f5e8;
            border-left: 4px solid #28a745;
            padding: 15px;
            margin: 15px 0;
            border-radius: 0 5px 5px 0;
        }
        
        .summary-box {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 25px;
            border-radius: 10px;
            margin: 30px 0;
            text-align: center;
        }
        
        .summary-box h4 {
            margin: 0 0 15px 0;
            font-size: 20px;
        }
        
        .summary-stats {
            display: flex;
            justify-content: space-around;
            flex-wrap: wrap;
            gap: 20px;
        }
        
        .stat-item {
            text-align: center;
        }
        
        .stat-number {
            font-size: 32px;
            font-weight: bold;
            display: block;
        }
        
        .stat-label {
            font-size: 14px;
            opacity: 0.9;
        }
        
        .page-break {
            page-break-before: always;
        }
        
        @media print {
            body {
                margin: 20px;
                background: white;
            }
            .section {
                box-shadow: none;
                border: 1px solid #ddd;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>E-R Diagram ระบบฐานข้อมูล</h1>
        <h2>เว็บไซต์ผู้ใหญ่ประจักษ์ เซอร์วิส ช็อป</h2>
        <p>วันที่จัดทำ: 21 กรกฎาคม 2568</p>
    </div>

    <div class="summary-box">
        <h4>สรุปภาพรวมระบบฐานข้อมูล</h4>
        <div class="summary-stats">
            <div class="stat-item">
                <span class="stat-number">10</span>
                <span class="stat-label">ตารางทั้งหมด</span>
            </div>
            <div class="stat-item">
                <span class="stat-number">2</span>
                <span class="stat-label">ความสัมพันธ์หลัก</span>
            </div>
            <div class="stat-item">
                <span class="stat-number">8</span>
                <span class="stat-label">ตารางใช้งานจริง</span>
            </div>
            <div class="stat-item">
                <span class="stat-number">2</span>
                <span class="stat-label">ตารางระบบ</span>
            </div>
        </div>
    </div>

    <div class="section">
        <h3>📊 รายละเอียดตารางฐานข้อมูล</h3>
        
        <div class="entity-name">1. ตาราง users - ผู้ดูแลระบบ</div>
        <div class="usage">💼 การใช้งาน: ระบบ Login Admin, จัดการสิทธิ์การเข้าถึง</div>
        <div class="table-container">
            <table>
                <thead>
                    <tr>
                        <th>ลำดับ</th>
                        <th>ชื่อฟิลด์</th>
                        <th>ประเภทข้อมูล</th>
                        <th>คีย์</th>
                        <th>คำอธิบาย</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>1</td>
                        <td>id</td>
                        <td>BIGINT</td>
                        <td><span class="key-pk">PK</span></td>
                        <td>รหัสผู้ดูแลระบบ (Auto Increment)</td>
                    </tr>
                    <tr>
                        <td>2</td>
                        <td>name</td>
                        <td>VARCHAR(255)</td>
                        <td>-</td>
                        <td>ชื่อผู้ดูแลระบบ</td>
                    </tr>
                    <tr>
                        <td>3</td>
                        <td>email</td>
                        <td>VARCHAR(255)</td>
                        <td><span class="key-uk">UNIQUE</span></td>
                        <td>อีเมลสำหรับเข้าสู่ระบบ</td>
                    </tr>
                    <tr>
                        <td>4</td>
                        <td>password</td>
                        <td>VARCHAR(255)</td>
                        <td>-</td>
                        <td>รหัสผ่าน (เข้ารหัส)</td>
                    </tr>
                    <tr>
                        <td>5</td>
                        <td>created_at</td>
                        <td>TIMESTAMP</td>
                        <td>-</td>
                        <td>วันที่สร้างบัญชี</td>
                    </tr>
                    <tr>
                        <td>6</td>
                        <td>updated_at</td>
                        <td>TIMESTAMP</td>
                        <td>-</td>
                        <td>วันที่แก้ไขล่าสุด</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div class="entity-name">2. ตาราง services - บริการ</div>
        <div class="usage">💼 การใช้งาน: แสดงในหน้าหลัก (6 บริการ), หน้าบริการทั้งหมด</div>
        <div class="table-container">
            <table>
                <thead>
                    <tr>
                        <th>ลำดับ</th>
                        <th>ชื่อฟิลด์</th>
                        <th>ประเภทข้อมูล</th>
                        <th>คีย์</th>
                        <th>คำอธิบาย</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>1</td>
                        <td>id</td>
                        <td>BIGINT</td>
                        <td><span class="key-pk">PK</span></td>
                        <td>รหัสบริการ (Auto Increment)</td>
                    </tr>
                    <tr>
                        <td>2</td>
                        <td>title</td>
                        <td>VARCHAR(255)</td>
                        <td>-</td>
                        <td>ชื่อบริการ</td>
                    </tr>
                    <tr>
                        <td>3</td>
                        <td>description</td>
                        <td>TEXT</td>
                        <td>-</td>
                        <td>คำอธิบายบริการ</td>
                    </tr>
                    <tr>
                        <td>4</td>
                        <td>details</td>
                        <td>TEXT</td>
                        <td>-</td>
                        <td>รายละเอียดเพิ่มเติม</td>
                    </tr>
                    <tr>
                        <td>5</td>
                        <td>image</td>
                        <td>VARCHAR(255)</td>
                        <td>-</td>
                        <td>รูปภาพหลักของบริการ</td>
                    </tr>
                    <tr>
                        <td>6</td>
                        <td>is_active</td>
                        <td>BOOLEAN</td>
                        <td>-</td>
                        <td>สถานะการใช้งาน (1=เปิด, 0=ปิด)</td>
                    </tr>
                    <tr>
                        <td>7</td>
                        <td>sort_order</td>
                        <td>INT</td>
                        <td>-</td>
                        <td>ลำดับการแสดงผล</td>
                    </tr>
                    <tr>
                        <td>8</td>
                        <td>created_at</td>
                        <td>TIMESTAMP</td>
                        <td>-</td>
                        <td>วันที่สร้าง</td>
                    </tr>
                    <tr>
                        <td>9</td>
                        <td>updated_at</td>
                        <td>TIMESTAMP</td>
                        <td>-</td>
                        <td>วันที่แก้ไขล่าสุด</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div class="entity-name">3. ตาราง service_images - รูปภาพบริการ</div>
        <div class="usage">💼 การใช้งาน: แกลเลอรี่ในหน้ารายละเอียดบริการ</div>
        <div class="relationship">🔗 ความสัมพันธ์: service_images.service_id → services.id (1 ต่อ หลาย)</div>
        <div class="table-container">
            <table>
                <thead>
                    <tr>
                        <th>ลำดับ</th>
                        <th>ชื่อฟิลด์</th>
                        <th>ประเภทข้อมูล</th>
                        <th>คีย์</th>
                        <th>คำอธิบาย</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>1</td>
                        <td>id</td>
                        <td>BIGINT</td>
                        <td><span class="key-pk">PK</span></td>
                        <td>รหัสรูปภาพ (Auto Increment)</td>
                    </tr>
                    <tr>
                        <td>2</td>
                        <td>service_id</td>
                        <td>BIGINT</td>
                        <td><span class="key-fk">FK</span></td>
                        <td>รหัสบริการที่เป็นเจ้าของรูป</td>
                    </tr>
                    <tr>
                        <td>3</td>
                        <td>image_path</td>
                        <td>VARCHAR(255)</td>
                        <td>-</td>
                        <td>ที่อยู่ไฟล์รูปภาพ</td>
                    </tr>
                    <tr>
                        <td>4</td>
                        <td>alt_text</td>
                        <td>VARCHAR(255)</td>
                        <td>-</td>
                        <td>ข้อความทดแทนรูปภาพ</td>
                    </tr>
                    <tr>
                        <td>5</td>
                        <td>description</td>
                        <td>TEXT</td>
                        <td>-</td>
                        <td>คำอธิบายรูปภาพ</td>
                    </tr>
                    <tr>
                        <td>6</td>
                        <td>is_cover</td>
                        <td>BOOLEAN</td>
                        <td>-</td>
                        <td>รูปหน้าปก (1=ใช่, 0=ไม่ใช่)</td>
                    </tr>
                    <tr>
                        <td>7</td>
                        <td>sort_order</td>
                        <td>INT</td>
                        <td>-</td>
                        <td>ลำดับการแสดงผล</td>
                    </tr>
                    <tr>
                        <td>8</td>
                        <td>created_at</td>
                        <td>TIMESTAMP</td>
                        <td>-</td>
                        <td>วันที่อัปโหลด</td>
                    </tr>
                    <tr>
                        <td>9</td>
                        <td>updated_at</td>
                        <td>TIMESTAMP</td>
                        <td>-</td>
                        <td>วันที่แก้ไขล่าสุด</td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>

    <div class="page-break"></div>

    <div class="section">
        <h3>📦 ตารางแพ็คเกจและผลงาน</h3>
        
        <div class="entity-name">4. ตาราง packages - แพ็คเกจบริการ</div>
        <div class="usage">💼 การใช้งาน: หน้าแพ็คเกจ, เปรียบเทียบราคา</div>
        <div class="table-container">
            <table>
                <thead>
                    <tr>
                        <th>ลำดับ</th>
                        <th>ชื่อฟิลด์</th>
                        <th>ประเภทข้อมูล</th>
                        <th>คีย์</th>
                        <th>คำอธิบาย</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>1</td>
                        <td>id</td>
                        <td>BIGINT</td>
                        <td><span class="key-pk">PK</span></td>
                        <td>รหัสแพ็คเกจ (Auto Increment)</td>
                    </tr>
                    <tr>
                        <td>2</td>
                        <td>title</td>
                        <td>VARCHAR(255)</td>
                        <td>-</td>
                        <td>ชื่อแพ็คเกจ</td>
                    </tr>
                    <tr>
                        <td>3</td>
                        <td>description</td>
                        <td>TEXT</td>
                        <td>-</td>
                        <td>คำอธิบายแพ็คเกจ</td>
                    </tr>
                    <tr>
                        <td>4</td>
                        <td>details</td>
                        <td>TEXT</td>
                        <td>-</td>
                        <td>รายละเอียดเพิ่มเติม</td>
                    </tr>
                    <tr>
                        <td>5</td>
                        <td>price</td>
                        <td>VARCHAR(255)</td>
                        <td>-</td>
                        <td>ราคา (เก็บเป็น String)</td>
                    </tr>
                    <tr>
                        <td>6</td>
                        <td>image</td>
                        <td>VARCHAR(255)</td>
                        <td>-</td>
                        <td>รูปภาพแพ็คเกจ</td>
                    </tr>
                    <tr>
                        <td>7</td>
                        <td>is_active</td>
                        <td>BOOLEAN</td>
                        <td>-</td>
                        <td>สถานะการใช้งาน</td>
                    </tr>
                    <tr>
                        <td>8</td>
                        <td>sort_order</td>
                        <td>INT</td>
                        <td>-</td>
                        <td>ลำดับการแสดงผล</td>
                    </tr>
                    <tr>
                        <td>9</td>
                        <td>created_at</td>
                        <td>TIMESTAMP</td>
                        <td>-</td>
                        <td>วันที่สร้าง</td>
                    </tr>
                    <tr>
                        <td>10</td>
                        <td>updated_at</td>
                        <td>TIMESTAMP</td>
                        <td>-</td>
                        <td>วันที่แก้ไขล่าสุด</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div class="entity-name">5. ตาราง activities - ผลงาน</div>
        <div class="usage">💼 การใช้งาน: หน้าหลัก (4 ผลงานแบบสุ่ม), หน้าผลงานทั้งหมด</div>
        <div class="table-container">
            <table>
                <thead>
                    <tr>
                        <th>ลำดับ</th>
                        <th>ชื่อฟิลด์</th>
                        <th>ประเภทข้อมูล</th>
                        <th>คีย์</th>
                        <th>คำอธิบาย</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>1</td>
                        <td>id</td>
                        <td>BIGINT</td>
                        <td><span class="key-pk">PK</span></td>
                        <td>รหัสผลงาน (Auto Increment)</td>
                    </tr>
                    <tr>
                        <td>2</td>
                        <td>title</td>
                        <td>VARCHAR(255)</td>
                        <td>-</td>
                        <td>ชื่อผลงาน</td>
                    </tr>
                    <tr>
                        <td>3</td>
                        <td>description</td>
                        <td>TEXT</td>
                        <td>-</td>
                        <td>คำอธิบายผลงาน</td>
                    </tr>
                    <tr>
                        <td>4</td>
                        <td>details</td>
                        <td>TEXT</td>
                        <td>-</td>
                        <td>รายละเอียดเพิ่มเติม</td>
                    </tr>
                    <tr>
                        <td>5</td>
                        <td>image</td>
                        <td>VARCHAR(255)</td>
                        <td>-</td>
                        <td>รูปภาพหลัก</td>
                    </tr>
                    <tr>
                        <td>6</td>
                        <td>is_active</td>
                        <td>BOOLEAN</td>
                        <td>-</td>
                        <td>สถานะการแสดงผล</td>
                    </tr>
                    <tr>
                        <td>7</td>
                        <td>sort_order</td>
                        <td>INT</td>
                        <td>-</td>
                        <td>ลำดับการแสดงผล</td>
                    </tr>
                    <tr>
                        <td>8</td>
                        <td>created_at</td>
                        <td>TIMESTAMP</td>
                        <td>-</td>
                        <td>วันที่สร้าง</td>
                    </tr>
                    <tr>
                        <td>9</td>
                        <td>updated_at</td>
                        <td>TIMESTAMP</td>
                        <td>-</td>
                        <td>วันที่แก้ไขล่าสุด</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div class="entity-name">6. ตาราง activity_images - รูปภาพผลงาน</div>
        <div class="usage">💼 การใช้งาน: แกลเลอรี่ในหน้ารายละเอียดผลงาน</div>
        <div class="relationship">🔗 ความสัมพันธ์: activity_images.activity_id → activities.id (1 ต่อ หลาย)</div>
        <div class="table-container">
            <table>
                <thead>
                    <tr>
                        <th>ลำดับ</th>
                        <th>ชื่อฟิลด์</th>
                        <th>ประเภทข้อมูล</th>
                        <th>คีย์</th>
                        <th>คำอธิบาย</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>1</td>
                        <td>id</td>
                        <td>BIGINT</td>
                        <td><span class="key-pk">PK</span></td>
                        <td>รหัสรูปภาพ (Auto Increment)</td>
                    </tr>
                    <tr>
                        <td>2</td>
                        <td>activity_id</td>
                        <td>BIGINT</td>
                        <td><span class="key-fk">FK</span></td>
                        <td>รหัสผลงานที่เป็นเจ้าของรูป</td>
                    </tr>
                    <tr>
                        <td>3</td>
                        <td>image_path</td>
                        <td>VARCHAR(255)</td>
                        <td>-</td>
                        <td>ที่อยู่ไฟล์รูปภาพ</td>
                    </tr>
                    <tr>
                        <td>4</td>
                        <td>alt_text</td>
                        <td>VARCHAR(255)</td>
                        <td>-</td>
                        <td>ข้อความทดแทนรูปภาพ</td>
                    </tr>
                    <tr>
                        <td>5</td>
                        <td>description</td>
                        <td>TEXT</td>
                        <td>-</td>
                        <td>คำอธิบายรูปภาพ</td>
                    </tr>
                    <tr>
                        <td>6</td>
                        <td>is_cover</td>
                        <td>BOOLEAN</td>
                        <td>-</td>
                        <td>รูปหน้าปก (1=ใช่, 0=ไม่ใช่)</td>
                    </tr>
                    <tr>
                        <td>7</td>
                        <td>sort_order</td>
                        <td>INT</td>
                        <td>-</td>
                        <td>ลำดับการแสดงผล</td>
                    </tr>
                    <tr>
                        <td>8</td>
                        <td>created_at</td>
                        <td>TIMESTAMP</td>
                        <td>-</td>
                        <td>วันที่อัปโหลด</td>
                    </tr>
                    <tr>
                        <td>9</td>
                        <td>updated_at</td>
                        <td>TIMESTAMP</td>
                        <td>-</td>
                        <td>วันที่แก้ไขล่าสุด</td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>

    <div class="page-break"></div>

    <div class="section">
        <h3>🔧 ตารางระบบและการตั้งค่า</h3>
        
        <div class="entity-name">7. ตาราง contacts - การติดต่อ</div>
        <div class="usage">💼 การใช้งาน: ระบบ Admin ดูข้อความจากลูกค้า, แจ้งเตือนข้อความใหม่</div>
        <div class="table-container">
            <table>
                <thead>
                    <tr>
                        <th>ลำดับ</th>
                        <th>ชื่อฟิลด์</th>
                        <th>ประเภทข้อมูล</th>
                        <th>คีย์</th>
                        <th>คำอธิบาย</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>1</td>
                        <td>id</td>
                        <td>BIGINT</td>
                        <td><span class="key-pk">PK</span></td>
                        <td>รหัสการติดต่อ (Auto Increment)</td>
                    </tr>
                    <tr>
                        <td>2</td>
                        <td>name</td>
                        <td>VARCHAR(255)</td>
                        <td>-</td>
                        <td>ชื่อผู้ติดต่อ</td>
                    </tr>
                    <tr>
                        <td>3</td>
                        <td>email</td>
                        <td>VARCHAR(255)</td>
                        <td>-</td>
                        <td>อีเมลผู้ติดต่อ</td>
                    </tr>
                    <tr>
                        <td>4</td>
                        <td>phone</td>
                        <td>VARCHAR(20)</td>
                        <td>-</td>
                        <td>เบอร์โทรศัพท์</td>
                    </tr>
                    <tr>
                        <td>5</td>
                        <td>subject</td>
                        <td>VARCHAR(255)</td>
                        <td>-</td>
                        <td>หัวข้อการติดต่อ</td>
                    </tr>
                    <tr>
                        <td>6</td>
                        <td>message</td>
                        <td>TEXT</td>
                        <td>-</td>
                        <td>ข้อความ</td>
                    </tr>
                    <tr>
                        <td>7</td>
                        <td>is_read</td>
                        <td>BOOLEAN</td>
                        <td>-</td>
                        <td>สถานะการอ่าน (0=ยังไม่อ่าน, 1=อ่านแล้ว)</td>
                    </tr>
                    <tr>
                        <td>8</td>
                        <td>created_at</td>
                        <td>TIMESTAMP</td>
                        <td>-</td>
                        <td>วันที่ส่งข้อความ</td>
                    </tr>
                    <tr>
                        <td>9</td>
                        <td>updated_at</td>
                        <td>TIMESTAMP</td>
                        <td>-</td>
                        <td>วันที่แก้ไขล่าสุด</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div class="entity-name">8. ตาราง banners - แบนเนอร์</div>
        <div class="usage">💼 การใช้งาน: สไลด์โชว์ในหน้าหลัก, หน้าบริการ</div>
        <div class="table-container">
            <table>
                <thead>
                    <tr>
                        <th>ลำดับ</th>
                        <th>ชื่อฟิลด์</th>
                        <th>ประเภทข้อมูล</th>
                        <th>คีย์</th>
                        <th>คำอธิบาย</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>1</td>
                        <td>id</td>
                        <td>BIGINT</td>
                        <td><span class="key-pk">PK</span></td>
                        <td>รหัสแบนเนอร์ (Auto Increment)</td>
                    </tr>
                    <tr>
                        <td>2</td>
                        <td>title</td>
                        <td>VARCHAR(255)</td>
                        <td>-</td>
                        <td>ชื่อแบนเนอร์</td>
                    </tr>
                    <tr>
                        <td>3</td>
                        <td>description</td>
                        <td>TEXT</td>
                        <td>-</td>
                        <td>คำอธิบายแบนเนอร์</td>
                    </tr>
                    <tr>
                        <td>4</td>
                        <td>image_path</td>
                        <td>VARCHAR(255)</td>
                        <td>-</td>
                        <td>ที่อยู่ไฟล์รูปภาพ</td>
                    </tr>
                    <tr>
                        <td>5</td>
                        <td>display_pages</td>
                        <td>JSON</td>
                        <td>-</td>
                        <td>หน้าที่ต้องการแสดง ['home', 'services']</td>
                    </tr>
                    <tr>
                        <td>6</td>
                        <td>is_active</td>
                        <td>BOOLEAN</td>
                        <td>-</td>
                        <td>สถานะการแสดงผล</td>
                    </tr>
                    <tr>
                        <td>7</td>
                        <td>sort_order</td>
                        <td>INT</td>
                        <td>-</td>
                        <td>ลำดับการแสดงผล</td>
                    </tr>
                    <tr>
                        <td>8</td>
                        <td>created_at</td>
                        <td>TIMESTAMP</td>
                        <td>-</td>
                        <td>วันที่สร้าง</td>
                    </tr>
                    <tr>
                        <td>9</td>
                        <td>updated_at</td>
                        <td>TIMESTAMP</td>
                        <td>-</td>
                        <td>วันที่แก้ไขล่าสุด</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div class="entity-name">9. ตาราง site_settings - การตั้งค่าเว็บไซต์</div>
        <div class="usage">💼 การใช้งาน: แสดงในทุกหน้าของเว็บไซต์ (Header, Footer)</div>
        <div class="table-container">
            <table>
                <thead>
                    <tr>
                        <th>ลำดับ</th>
                        <th>ชื่อฟิลด์</th>
                        <th>ประเภทข้อมูล</th>
                        <th>คีย์</th>
                        <th>คำอธิบาย</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>1</td>
                        <td>id</td>
                        <td>BIGINT</td>
                        <td><span class="key-pk">PK</span></td>
                        <td>รหัสการตั้งค่า (Auto Increment)</td>
                    </tr>
                    <tr>
                        <td>2</td>
                        <td>key</td>
                        <td>VARCHAR(255)</td>
                        <td><span class="key-uk">UNIQUE</span></td>
                        <td>คีย์การตั้งค่า (เช่น site_name, phone)</td>
                    </tr>
                    <tr>
                        <td>3</td>
                        <td>value</td>
                        <td>TEXT</td>
                        <td>-</td>
                        <td>ค่าการตั้งค่า</td>
                    </tr>
                    <tr>
                        <td>4</td>
                        <td>description</td>
                        <td>TEXT</td>
                        <td>-</td>
                        <td>คำอธิบายการตั้งค่า</td>
                    </tr>
                    <tr>
                        <td>5</td>
                        <td>created_at</td>
                        <td>TIMESTAMP</td>
                        <td>-</td>
                        <td>วันที่สร้าง</td>
                    </tr>
                    <tr>
                        <td>6</td>
                        <td>updated_at</td>
                        <td>TIMESTAMP</td>
                        <td>-</td>
                        <td>วันที่แก้ไขล่าสุด</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div class="entity-name">10. ตาราง migrations - ระบบ Laravel</div>
        <div class="usage">💼 การใช้งาน: ระบบ Laravel ใช้ติดตามการเปลี่ยนแปลงฐานข้อมูล</div>
        <div class="table-container">
            <table>
                <thead>
                    <tr>
                        <th>ลำดับ</th>
                        <th>ชื่อฟิลด์</th>
                        <th>ประเภทข้อมูล</th>
                        <th>คีย์</th>
                        <th>คำอธิบาย</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>1</td>
                        <td>id</td>
                        <td>INT</td>
                        <td><span class="key-pk">PK</span></td>
                        <td>รหัสลำดับการรัน</td>
                    </tr>
                    <tr>
                        <td>2</td>
                        <td>migration</td>
                        <td>VARCHAR(255)</td>
                        <td>-</td>
                        <td>ชื่อไฟล์ migration</td>
                    </tr>
                    <tr>
                        <td>3</td>
                        <td>batch</td>
                        <td>INT</td>
                        <td>-</td>
                        <td>กลุ่มการรัน migration</td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>

    <div class="section">
        <h3>🔗 ความสัมพันธ์ระหว่างตาราง</h3>
        
        <div class="relationship">
            <strong>ความสัมพันธ์หลัก:</strong><br>
            1. services (1) → service_images (หลาย)<br>
            2. activities (1) → activity_images (หลาย)
        </div>
        
        <div class="relationship">
            <strong>ตารางที่ใช้งานหลัก:</strong><br>
            • users: ระบบ Admin<br>
            • services: หน้าหลัก, หน้าบริการ<br>
            • packages: หน้าแพ็คเกจ<br>
            • activities: หน้าหลัก, หน้าผลงาน<br>
            • banners: สไลด์โชว์ทุกหน้า<br>
            • site_settings: ข้อมูลติดต่อทุกหน้า<br>
            • contacts: ระบบ Admin
        </div>
    </div>

    <div class="summary-box">
        <h4>สรุปการออกแบบฐานข้อมูล</h4>
        <p>ระบบฐานข้อมูลนี้ออกแบบมาเพื่อรองรับเว็บไซต์บริการจัดงานศพ โดยมีโครงสร้างที่ยืดหยุ่น สามารถขยายได้ และรองรับการจัดการข้อมูลได้อย่างมีประสิทธิภาพ</p>
    </div>

</body>
</html>
