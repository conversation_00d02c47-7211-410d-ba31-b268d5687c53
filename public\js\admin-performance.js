// Phuyai Prajak Service Shop Admin Performance Optimizations

// Debounce function for performance
function debounce(func, wait, immediate) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            timeout = null;
            if (!immediate) func(...args);
        };
        const callNow = immediate && !timeout;
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
        if (callNow) func(...args);
    };
}

// Throttle function for scroll events
function throttle(func, limit) {
    let inThrottle;
    return function() {
        const args = arguments;
        const context = this;
        if (!inThrottle) {
            func.apply(context, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    }
}

// Intersection Observer for lazy loading
const createIntersectionObserver = (callback, options = {}) => {
    const defaultOptions = {
        root: null,
        rootMargin: '50px',
        threshold: 0.1
    };
    
    return new IntersectionObserver(callback, { ...defaultOptions, ...options });
};

// Image lazy loading with smooth fade-in
function initImageLazyLoading() {
    const images = document.querySelectorAll('img[data-src]');
    
    const imageObserver = createIntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const img = entry.target;
                
                // Create a new image to preload
                const newImg = new Image();
                newImg.onload = () => {
                    img.src = newImg.src;
                    img.classList.add('loaded');
                    img.style.opacity = '1';
                };
                newImg.src = img.dataset.src;
                
                imageObserver.unobserve(img);
            }
        });
    });
    
    images.forEach(img => {
        img.style.opacity = '0';
        img.style.transition = 'opacity 0.3s ease';
        imageObserver.observe(img);
    });
}

// Smooth scroll with performance optimization
function initSmoothScroll() {
    let isScrolling = false;
    
    const smoothScrollTo = (target, duration = 800) => {
        if (isScrolling) return;
        isScrolling = true;
        
        const start = window.pageYOffset;
        const distance = target.offsetTop - start;
        const startTime = performance.now();
        
        const easeInOutCubic = (t) => {
            return t < 0.5 ? 4 * t * t * t : (t - 1) * (2 * t - 2) * (2 * t - 2) + 1;
        };
        
        const animateScroll = (currentTime) => {
            const timeElapsed = currentTime - startTime;
            const progress = Math.min(timeElapsed / duration, 1);
            const ease = easeInOutCubic(progress);
            
            window.scrollTo(0, start + distance * ease);
            
            if (progress < 1) {
                requestAnimationFrame(animateScroll);
            } else {
                isScrolling = false;
            }
        };
        
        requestAnimationFrame(animateScroll);
    };
    
    // Bind to anchor links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function(e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                smoothScrollTo(target);
            }
        });
    });
}

// Performance monitoring
function initPerformanceMonitoring() {
    // Monitor page load time
    window.addEventListener('load', () => {
        const loadTime = performance.now();
        console.log(`Page loaded in ${loadTime.toFixed(2)}ms`);
        
        // Send to analytics if needed
        if (loadTime > 3000) {
            console.warn('Page load time is slow:', loadTime);
        }
    });
    
    // Monitor memory usage (if available)
    if ('memory' in performance) {
        setInterval(() => {
            const memory = performance.memory;
            if (memory.usedJSHeapSize > memory.jsHeapSizeLimit * 0.9) {
                console.warn('High memory usage detected');
            }
        }, 30000); // Check every 30 seconds
    }
}

// Optimize animations with requestAnimationFrame
class AnimationQueue {
    constructor() {
        this.queue = [];
        this.isRunning = false;
    }
    
    add(animation) {
        this.queue.push(animation);
        if (!this.isRunning) {
            this.run();
        }
    }
    
    run() {
        this.isRunning = true;
        
        const processQueue = () => {
            if (this.queue.length === 0) {
                this.isRunning = false;
                return;
            }
            
            const animation = this.queue.shift();
            animation();
            
            requestAnimationFrame(processQueue);
        };
        
        requestAnimationFrame(processQueue);
    }
}

// Global animation queue
window.animationQueue = new AnimationQueue();

// Optimize table rendering for large datasets
function optimizeTableRendering() {
    const tables = document.querySelectorAll('.table tbody');
    
    tables.forEach(tbody => {
        const rows = Array.from(tbody.children);
        if (rows.length > 50) {
            // Implement virtual scrolling for large tables
            implementVirtualScrolling(tbody, rows);
        }
    });
}

function implementVirtualScrolling(tbody, rows) {
    const container = tbody.parentElement;
    const rowHeight = 60; // Approximate row height
    const visibleRows = Math.ceil(container.clientHeight / rowHeight) + 5;
    
    let startIndex = 0;
    let endIndex = Math.min(visibleRows, rows.length);
    
    const renderVisibleRows = throttle(() => {
        // Clear current rows
        tbody.innerHTML = '';
        
        // Render visible rows
        for (let i = startIndex; i < endIndex; i++) {
            if (rows[i]) {
                tbody.appendChild(rows[i]);
            }
        }
    }, 16); // ~60fps
    
    // Handle scroll events
    container.addEventListener('scroll', () => {
        const scrollTop = container.scrollTop;
        startIndex = Math.floor(scrollTop / rowHeight);
        endIndex = Math.min(startIndex + visibleRows, rows.length);
        
        renderVisibleRows();
    });
    
    // Initial render
    renderVisibleRows();
}

// Initialize all performance optimizations
document.addEventListener('DOMContentLoaded', () => {
    initImageLazyLoading();
    initSmoothScroll();
    initPerformanceMonitoring();
    optimizeTableRendering();
});

// Export for global use
window.AdminPerformance = {
    debounce,
    throttle,
    createIntersectionObserver,
    animationQueue
};
