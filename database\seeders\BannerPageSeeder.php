<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Banner;

class BannerPageSeeder extends Seeder
{
    public function run()
    {
        // ลบแบนเนอร์เก่าทั้งหมด
        Banner::truncate();

        $banners = [
            [
                'title' => 'แบนเนอร์หน้าหลัก',
                'description' => 'แบนเนอร์สำหรับหน้าหลักเท่านั้น',
                'image_path' => 'banners/home-banner.jpg',
                'link_type' => 'services',
                'display_pages' => ['home'],
                'is_active' => true,
                'sort_order' => 1,
            ],
            [
                'title' => 'แบนเนอร์หน้าบริการ',
                'description' => 'แบนเนอร์สำหรับหน้าบริการเท่านั้น',
                'image_path' => 'banners/services-banner.jpg',
                'link_type' => 'packages',
                'display_pages' => ['services'],
                'is_active' => true,
                'sort_order' => 1,
            ],
            [
                'title' => 'แบนเนอร์หน้าแพ็คเกจ',
                'description' => 'แบนเนอร์สำหรับหน้าแพ็คเกจเท่านั้น',
                'image_path' => 'banners/packages-banner.jpg',
                'link_type' => 'contact',
                'display_pages' => ['packages'],
                'is_active' => true,
                'sort_order' => 1,
            ],
            [
                'title' => 'แบนเนอร์หน้าผลงาน',
                'description' => 'แบนเนอร์สำหรับหน้าผลงานเท่านั้น',
                'image_path' => 'banners/activities-banner.jpg',
                'link_type' => 'services',
                'display_pages' => ['activities'],
                'is_active' => true,
                'sort_order' => 1,
            ],
            [
                'title' => 'แบนเนอร์หน้าติดต่อเรา',
                'description' => 'แบนเนอร์สำหรับหน้าติดต่อเราเท่านั้น',
                'image_path' => 'banners/contact-banner.jpg',
                'link_type' => 'none',
                'display_pages' => ['contact'],
                'is_active' => true,
                'sort_order' => 1,
            ],
            [
                'title' => 'แบนเนอร์ทุกหน้า',
                'description' => 'แบนเนอร์ที่แสดงในทุกหน้า',
                'image_path' => 'banners/global-banner.jpg',
                'link_type' => 'custom',
                'link_url' => 'https://example.com',
                'display_pages' => null, // แสดงในทุกหน้า
                'is_active' => false, // ปิดไว้ก่อน
                'sort_order' => 2,
            ],
        ];

        foreach ($banners as $bannerData) {
            Banner::create($bannerData);
        }

        echo "Created " . count($banners) . " sample banners with page filters!\n";
    }
}
