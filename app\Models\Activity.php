<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Activity extends Model
{
    use HasFactory;

    protected $fillable = [
        'title',
        'description',
        'details',
        'image',
        'activity_date',
        'location',
        'is_active',
        'sort_order'
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'activity_date' => 'date'
    ];

    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order')->orderBy('activity_date', 'desc');
    }

    public function scopeRecent($query)
    {
        return $query->orderBy('activity_date', 'desc');
    }

    public function images()
    {
        return $this->hasMany(ActivityImage::class);
    }

    public function coverImage()
    {
        return $this->hasOne(ActivityImage::class)->where('is_cover', true);
    }

    public function getCoverImageAttribute()
    {
        $coverImage = $this->coverImage;
        if ($coverImage) {
            return $coverImage->image_path;
        }

        // Fallback to first image if no cover image
        $firstImage = $this->images()->ordered()->first();
        if ($firstImage) {
            return $firstImage->image_path;
        }

        // Fallback to old image field
        return $this->image;
    }
}
