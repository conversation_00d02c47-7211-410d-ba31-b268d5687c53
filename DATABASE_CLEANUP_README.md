# การจัดการตารางที่ไม่ได้ใช้งานในฐานข้อมูล

## สรุปผลการตรวจสอบ

จากการตรวจสอบฐานข้อมูล **phuyai_prajak_service_shop** พบว่ามีตารางที่ไม่ได้ใช้งานจริงในเว็บไซต์ **5 ตาราง** จากทั้งหมด **16 ตาราง**

### ✅ ตารางที่ใช้งาน (11 ตาราง)
- `users` - ระบบผู้ใช้งาน Admin
- `services` - บริการต่างๆ
- `service_categories` - หมวดหมู่บริการ  
- `service_images` - รูปภาพบริการ
- `packages` - แพ็คเกจบริการ
- `activities` - ผลงานการให้บริการ
- `activity_images` - รูปภาพผลงาน
- `contacts` - การติดต่อจากลูกค้า
- `site_settings` - การตั้งค่าเว็บไซต์
- `banners` - แบนเนอร์สไลด์โชว์
- `migrations` - ตารางระบบ Laravel

### ❌ ตารางที่ไม่ได้ใช้งาน (5 ตาราง)
1. **`password_resets`** - ตารางรีเซ็ตรหัสผ่าน (ไม่มีฟีเจอร์นี้ในเว็บไซต์)
2. **`failed_jobs`** - ตาราง Queue Jobs ที่ล้มเหลว (ไม่ได้ใช้ระบบ Queue)
3. **`personal_access_tokens`** - ตาราง API Tokens (ไม่ได้ใช้ระบบ API)
4. **`page_backgrounds`** - ตารางพื้นหลังหน้าเว็บ (ใช้ระบบ Banner แทน)
5. **`page_background_images`** - ตารางรูปภาพพื้นหลัง (ใช้ระบบ Banner แทน)

## สคริปต์ที่สร้างขึ้น

### 1. `check-unused-tables.php`
**วัตถุประสงค์:** ตรวจสอบและแสดงรายการตารางที่ใช้งานและไม่ได้ใช้งาน

**วิธีใช้:**
```bash
php check-unused-tables.php
```

**ผลลัพธ์:**
- แสดงรายการตารางทั้งหมดพร้อมสถานะการใช้งาน
- อธิบายการใช้งานของแต่ละตาราง
- สรุปจำนวนตารางที่ใช้งานและไม่ได้ใช้งาน

### 2. `backup-before-cleanup.php`
**วัตถุประสงค์:** สำรองข้อมูลตารางที่ไม่ได้ใช้งานก่อนลบ

**วิธีใช้:**
```bash
php backup-before-cleanup.php
```

**ผลลัพธ์:**
- สร้างไฟล์สำรอง `.sql` ในโฟลเดอร์ `backups/`
- สำรองทั้งโครงสร้างและข้อมูลของตารางที่ไม่ได้ใช้งาน
- แสดงรายละเอียดการสำรอง

### 3. `remove-unused-tables.php`
**วัตถุประสงค์:** ลบตารางที่ไม่ได้ใช้งานออกจากฐานข้อมูล

**วิธีใช้:**
```bash
php remove-unused-tables.php
```

**คุณสมบัติ:**
- ขอการยืนยันก่อนลบ (ต้องพิมพ์ 'YES')
- แสดงรายการตารางที่จะลบพร้อมจำนวนข้อมูล
- สรุปผลการลบ

## ขั้นตอนการใช้งานที่แนะนำ

### ขั้นตอนที่ 1: ตรวจสอบตาราง
```bash
php check-unused-tables.php
```

### ขั้นตอนที่ 2: สำรองข้อมูล (ถ้าต้องการลบ)
```bash
php backup-before-cleanup.php
```

### ขั้นตอนที่ 3: ลบตารางที่ไม่ได้ใช้งาน (ถ้าต้องการ)
```bash
php remove-unused-tables.php
```

## ⚠️ ข้อควรระวัง

1. **สำรองข้อมูลก่อนเสมอ** - การลบตารางไม่สามารถกู้คืนได้
2. **ตรวจสอบการทำงานของเว็บไซต์** หลังจากลบตาราง
3. **ไม่ควรลบตาราง `migrations`** เพราะเป็นตารางระบบ Laravel
4. **พิจารณาแผนการใช้งานในอนาคต** ก่อนลบตาราง

## ประโยชน์ของการลบตารางที่ไม่ได้ใช้งาน

1. **ลดขนาดฐานข้อมูล** - ประหยัดพื้นที่จัดเก็บ
2. **เพิ่มประสิทธิภาพ** - ลดเวลาในการสำรองข้อมูล
3. **ความสะอาด** - ฐานข้อมูลมีเฉพาะตารางที่ใช้งานจริง
4. **ง่ายต่อการจัดการ** - ลดความซับซ้อนในการดูแลระบบ

## วิธีกู้คืนข้อมูล (ถ้าจำเป็น)

หากต้องการกู้คืนตารางที่ลบไปแล้ว:

```bash
mysql -u username -p database_name < backups/unused_tables_backup_YYYY-MM-DD_HH-mm-ss.sql
```

## การสำรองข้อมูลทั้งฐานข้อมูล

ก่อนดำเนินการใดๆ ควรสำรองข้อมูลทั้งฐานข้อมูล:

```bash
mysqldump -u username -p phuyai_prajak_service_shop > full_backup_$(date +%Y-%m-%d_%H-%M-%S).sql
```

## สรุป

ตารางที่ไม่ได้ใช้งานทั้ง 5 ตารางสามารถลบได้อย่างปลอดภัยโดยไม่ส่งผลกระทบต่อการทำงานของเว็บไซต์ เนื่องจาก:

- ไม่มีการเรียกใช้ใน Controllers
- ไม่มีการแสดงผลใน Views  
- ไม่มีฟีเจอร์ที่เกี่ยวข้องในเว็บไซต์

การลบตารางเหล่านี้จะช่วยให้ฐานข้อมูลสะอาดและมีประสิทธิภาพมากขึ้น
