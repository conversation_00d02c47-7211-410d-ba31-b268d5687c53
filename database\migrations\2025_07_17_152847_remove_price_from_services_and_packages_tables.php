<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // Remove price column from services table
        Schema::table('services', function (Blueprint $table) {
            $table->dropColumn('price');
        });

        // Remove price column from packages table
        Schema::table('packages', function (Blueprint $table) {
            $table->dropColumn('price');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        // Add price column back to services table
        Schema::table('services', function (Blueprint $table) {
            $table->decimal('price', 10, 2)->nullable()->after('details');
        });

        // Add price column back to packages table
        Schema::table('packages', function (Blueprint $table) {
            $table->decimal('price', 10, 2)->after('features');
        });
    }
};
