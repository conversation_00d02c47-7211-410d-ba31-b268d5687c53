@extends('layouts.admin')

@section('title', 'ดูข้อความติดต่อ - ระบบจัดการ')

@section('breadcrumb')
<li class="breadcrumb-item"><a href="{{ route('admin.contacts') }}">ข้อความติดต่อ</a></li>
<li class="breadcrumb-item active">ดูข้อความ</li>
@endsection

@section('content')
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0">ข้อความติดต่อ</h1>
    <div class="d-flex gap-2">
        <a href="{{ route('admin.contacts') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-left me-2"></i>กลับ
        </a>
        <form action="{{ route('admin.contacts.delete', $contact->id) }}" method="POST" class="d-inline"
              id="deleteContactForm{{ $contact->id }}">
            @csrf
            @method('DELETE')
            <button type="button" class="btn btn-outline-danger"
                    onclick="handleDeleteContact({{ $contact->id }})">
                <i class="fas fa-trash me-2"></i>ลบ
            </button>
        </form>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">{{ $contact->subject }}</h5>
                @if($contact->is_read)
                <span class="badge bg-success">อ่านแล้ว</span>
                @else
                <span class="badge bg-warning text-dark">ใหม่</span>
                @endif
            </div>
            <div class="card-body">
                <div class="message-content">
                    <div class="mb-4 p-3 bg-light rounded">
                        <h6 class="mb-2">ข้อความ:</h6>
                        <p class="mb-0" style="white-space: pre-wrap;">{{ $contact->message }}</p>
                    </div>
                </div>
                
                <!-- Quick Actions -->
                <div class="d-flex gap-2 flex-wrap">
                    <a href="mailto:{{ $contact->email }}?subject=Re: {{ $contact->subject }}" 
                       class="btn btn-primary">
                        <i class="fas fa-reply me-2"></i>ตอบกลับทางอีเมล
                    </a>
                    
                    @if($contact->phone)
                    <a href="tel:{{ $contact->phone }}" class="btn btn-success">
                        <i class="fas fa-phone me-2"></i>โทรหา
                    </a>
                    @endif
                    
                    <button type="button" class="btn btn-info" onclick="copyToClipboard('{{ $contact->email }}')">
                        <i class="fas fa-copy me-2"></i>คัดลอกอีเมล
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">ข้อมูลผู้ติดต่อ</h5>
            </div>
            <div class="card-body">
                <table class="table table-sm">
                    <tr>
                        <td><strong>ชื่อ:</strong></td>
                        <td>{{ $contact->name }}</td>
                    </tr>
                    <tr>
                        <td><strong>อีเมล:</strong></td>
                        <td>
                            <a href="mailto:{{ $contact->email }}" class="text-decoration-none">
                                {{ $contact->email }}
                            </a>
                        </td>
                    </tr>
                    <tr>
                        <td><strong>โทรศัพท์:</strong></td>
                        <td>
                            @if($contact->phone)
                            <a href="tel:{{ $contact->phone }}" class="text-decoration-none">
                                {{ $contact->phone }}
                            </a>
                            @else
                            <span class="text-muted">ไม่ระบุ</span>
                            @endif
                        </td>
                    </tr>
                    <tr>
                        <td><strong>หัวข้อ:</strong></td>
                        <td>{{ $contact->subject }}</td>
                    </tr>
                    <tr>
                        <td><strong>วันที่ส่ง:</strong></td>
                        <td>
                            {{ $contact->created_at->format('d/m/Y H:i') }}
                            <br>
                            <small class="text-muted">{{ $contact->created_at->diffForHumans() }}</small>
                        </td>
                    </tr>
                    <tr>
                        <td><strong>สถานะ:</strong></td>
                        <td>
                            @if($contact->is_read)
                            <span class="badge bg-success">อ่านแล้ว</span>
                            @else
                            <span class="badge bg-warning text-dark">ใหม่</span>
                            @endif
                        </td>
                    </tr>
                </table>
            </div>
        </div>
        
        <!-- Email Template -->
        <div class="card mt-3">
            <div class="card-header">
                <h5 class="card-title mb-0">แม่แบบการตอบกลับ</h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <label class="form-label">เลือกแม่แบบ:</label>
                    <select class="form-select" id="emailTemplate" onchange="fillEmailTemplate()">
                        <option value="">-- เลือกแม่แบบ --</option>
                        <option value="thanks">ขอบคุณสำหรับการติดต่อ</option>
                        <option value="quote">ส่งใบเสนอราคา</option>
                        <option value="info">ขอข้อมูลเพิ่มเติม</option>
                        <option value="appointment">นัดหมายพบ</option>
                    </select>
                </div>
                
                <div class="mb-3">
                    <label class="form-label">เนื้อหา:</label>
                    <textarea class="form-control" id="emailContent" rows="6" readonly></textarea>
                </div>
                
                <button type="button" class="btn btn-primary w-100" onclick="openEmailClient()">
                    <i class="fas fa-envelope me-2"></i>เปิดโปรแกรมอีเมล
                </button>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(function() {
        alert('คัดลอกอีเมลเรียบร้อยแล้ว');
    });
}

function fillEmailTemplate() {
    const template = document.getElementById('emailTemplate').value;
    const content = document.getElementById('emailContent');
    
    const templates = {
        'thanks': `เรียน คุณ{{ $contact->name }}

ขอบคุณสำหรับการติดต่อเรา เราได้รับข้อความของคุณเรียบร้อยแล้ว

ทีมงานของเราจะตรวจสอบและติดต่อกลับไปยังคุณในเร็วๆ นี้

ขอบคุณครับ/ค่ะ
{{ Auth::user()->name }}`,
        
        'quote': `เรียน คุณ{{ $contact->name }}

ขอบคุณสำหรับความสนใจในบริการของเรา

เราได้เตรียมใบเสนอราคาตามความต้องการของคุณแล้ว กรุณาตรวจสอบเอกสารแนบ

หากมีข้อสงสัยประการใด กรุณาติดต่อเราได้ตลอดเวลา

ขอบคุณครับ/ค่ะ
{{ Auth::user()->name }}`,
        
        'info': `เรียน คุณ{{ $contact->name }}

ขอบคุณสำหรับการติดต่อเรา

เพื่อให้เราสามารถให้คำแนะนำที่เหมาะสมที่สุด กรุณาแจ้งข้อมูลเพิ่มเติมดังนี้:
- รายละเอียดความต้องการ
- งบประมาณโดยประมาณ
- กรอบเวลาที่ต้องการ

ขอบคุณครับ/ค่ะ
{{ Auth::user()->name }}`,
        
        'appointment': `เรียน คุณ{{ $contact->name }}

ขอบคุณสำหรับความสนใจในบริการของเรา

เราขอเชิญคุณมาพบปะเพื่อหารือรายละเอียดเพิ่มเติม

กรุณาแจ้งวันและเวลาที่สะดวกให้เราทราบ

ขอบคุณครับ/ค่ะ
{{ Auth::user()->name }}`
    };
    
    content.value = templates[template] || '';
}

function openEmailClient() {
    const subject = encodeURIComponent('Re: {{ $contact->subject }}');
    const body = encodeURIComponent(document.getElementById('emailContent').value);
    const email = '{{ $contact->email }}';

    window.location.href = `mailto:${email}?subject=${subject}&body=${body}`;
}

// Delete contact function with custom modal
async function handleDeleteContact(contactId) {
    const confirmed = await confirmDelete(
        'คุณแน่ใจหรือไม่ที่จะลบข้อความนี้? การกระทำนี้ไม่สามารถยกเลิกได้',
        'ยืนยันการลบข้อความ'
    );

    if (confirmed) {
        document.getElementById(`deleteContactForm${contactId}`).submit();
    }
}
</script>
@endsection
