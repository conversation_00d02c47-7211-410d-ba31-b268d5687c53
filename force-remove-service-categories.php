<?php
/**
 * ลบตาราง service_categories โดยจัดการ Foreign Key Constraints
 */

require_once __DIR__ . '/vendor/autoload.php';

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

// Bootstrap Laravel
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

echo "========================================\n";
echo "   ลบตาราง service_categories (Force)\n";
echo "========================================\n\n";

try {
    // ตรวจสอบการเชื่อมต่อฐานข้อมูล
    DB::connection()->getPdo();
    echo "✅ การเชื่อมต่อฐานข้อมูล: สำเร็จ\n\n";

    // ตรวจสอบตาราง
    if (!Schema::hasTable('service_categories')) {
        echo "❌ ไม่พบตาราง service_categories\n";
        exit(0);
    }

    $categoryCount = DB::table('service_categories')->count();
    echo "📊 จำนวนหมวดหมู่: {$categoryCount} รายการ\n";

    // ตรวจสอบบริการที่อ้างอิง
    $servicesWithCategory = 0;
    if (Schema::hasColumn('services', 'category_id')) {
        $servicesWithCategory = DB::table('services')->whereNotNull('category_id')->count();
    }
    echo "📋 บริการที่อ้างอิงหมวดหมู่: {$servicesWithCategory} รายการ\n\n";

    echo "🚀 เริ่มลบตาราง service_categories...\n";

    // 1. ปิด Foreign Key Checks
    echo "1. ปิด Foreign Key Checks...\n";
    DB::statement('SET FOREIGN_KEY_CHECKS=0');
    echo "✅ ปิด Foreign Key Checks แล้ว\n";

    // 2. ลบการอ้างอิงในตาราง services (ถ้ามี)
    if (Schema::hasColumn('services', 'category_id') && $servicesWithCategory > 0) {
        echo "2. ลบการอ้างอิงหมวดหมู่ในตาราง services...\n";
        DB::table('services')->whereNotNull('category_id')->update(['category_id' => null]);
        echo "✅ ลบการอ้างอิงแล้ว\n";
    }

    // 3. ลบ Foreign Key Constraint (ถ้ามี)
    echo "3. ตรวจสอบและลบ Foreign Key Constraints...\n";
    
    // ตรวจสอบ constraints ที่มีอยู่
    $constraints = DB::select("
        SELECT CONSTRAINT_NAME 
        FROM information_schema.KEY_COLUMN_USAGE 
        WHERE TABLE_SCHEMA = DATABASE() 
        AND TABLE_NAME = 'services' 
        AND COLUMN_NAME = 'category_id' 
        AND REFERENCED_TABLE_NAME = 'service_categories'
    ");

    foreach ($constraints as $constraint) {
        echo "   ลบ constraint: {$constraint->CONSTRAINT_NAME}\n";
        DB::statement("ALTER TABLE services DROP FOREIGN KEY {$constraint->CONSTRAINT_NAME}");
    }
    echo "✅ ลบ Foreign Key Constraints แล้ว\n";

    // 4. ลบ column category_id จากตาราง services
    if (Schema::hasColumn('services', 'category_id')) {
        echo "4. ลบ column category_id จากตาราง services...\n";
        Schema::table('services', function ($table) {
            $table->dropColumn('category_id');
        });
        echo "✅ ลบ column category_id แล้ว\n";
    }

    // 5. ลบตาราง service_categories
    echo "5. ลบตาราง service_categories...\n";
    Schema::dropIfExists('service_categories');
    echo "✅ ลบตาราง service_categories แล้ว\n";

    // 6. เปิด Foreign Key Checks กลับ
    echo "6. เปิด Foreign Key Checks กลับ...\n";
    DB::statement('SET FOREIGN_KEY_CHECKS=1');
    echo "✅ เปิด Foreign Key Checks แล้ว\n";

    echo "\n🎉 ลบตาราง service_categories เสร็จสิ้น!\n";
    echo "----------------------------------------\n";
    echo "✅ ลบตาราง service_categories\n";
    echo "✅ ลบการอ้างอิงใน services\n";
    echo "✅ ลบ column category_id\n";
    echo "✅ ลบ Foreign Key Constraints\n\n";

    // แสดงตารางที่เหลือ
    $databaseName = config('database.connections.mysql.database');
    $tables = DB::select("SHOW TABLES");
    $tableColumn = "Tables_in_{$databaseName}";
    
    echo "📋 ตารางที่เหลือ: " . count($tables) . " ตาราง\n";
    foreach ($tables as $table) {
        $tableName = $table->$tableColumn;
        $count = DB::table($tableName)->count();
        echo "   - {$tableName} ({$count} รายการ)\n";
    }

    echo "\n📝 สิ่งที่ควรทำต่อไป:\n";
    echo "1. ลบไฟล์ app/Models/ServiceCategory.php\n";
    echo "2. ลบการ import ServiceCategory ใน Controllers\n";
    echo "3. ลบการใช้งาน category ใน Views\n";
    echo "4. ทดสอบเว็บไซต์ว่ายังทำงานปกติ\n";

} catch (Exception $e) {
    echo "❌ เกิดข้อผิดพลาด: " . $e->getMessage() . "\n";
    echo "📍 ไฟล์: " . $e->getFile() . " บรรทัด: " . $e->getLine() . "\n";
    
    // เปิด Foreign Key Checks กลับในกรณีเกิดข้อผิดพลาด
    try {
        DB::statement('SET FOREIGN_KEY_CHECKS=1');
        echo "✅ เปิด Foreign Key Checks กลับแล้ว\n";
    } catch (Exception $e2) {
        echo "❌ ไม่สามารถเปิด Foreign Key Checks กลับได้\n";
    }
}

echo "\n========================================\n";
echo "   เสร็จสิ้น\n";
echo "========================================\n";
?>
