<?php
/**
 * ลบตาราง service_categories และทำความสะอาดโค้ดที่เกี่ยวข้อง
 * เนื่องจากไม่ได้ใช้งานจริงในเว็บไซต์
 */

require_once __DIR__ . '/vendor/autoload.php';

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

// Bootstrap Laravel
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

echo "========================================\n";
echo "   ลบตาราง service_categories\n";
echo "========================================\n\n";

try {
    // ตรวจสอบการเชื่อมต่อฐานข้อมูล
    echo "🔍 ตรวจสอบการเชื่อมต่อฐานข้อมูล...\n";
    DB::connection()->getPdo();
    echo "✅ การเชื่อมต่อฐานข้อมูล: สำเร็จ\n\n";

    // ตรวจสอบข้อมูลในตาราง service_categories
    echo "📊 ตรวจสอบข้อมูลในตาราง service_categories:\n";
    echo "----------------------------------------\n";
    
    if (Schema::hasTable('service_categories')) {
        $categoryCount = DB::table('service_categories')->count();
        echo "จำนวนหมวดหมู่: {$categoryCount} รายการ\n";
        
        if ($categoryCount > 0) {
            echo "รายการหมวดหมู่:\n";
            $categories = DB::table('service_categories')->get();
            foreach ($categories as $category) {
                echo "- ID: {$category->id} | {$category->name}\n";
            }
        }
    } else {
        echo "❌ ไม่พบตาราง service_categories\n";
        exit(0);
    }

    // ตรวจสอบบริการที่อ้างอิงหมวดหมู่
    echo "\n📋 ตรวจสอบบริการที่อ้างอิงหมวดหมู่:\n";
    echo "----------------------------------------\n";
    $servicesWithCategory = DB::table('services')->whereNotNull('category_id')->count();
    $servicesWithoutCategory = DB::table('services')->whereNull('category_id')->count();
    echo "บริการที่มีหมวดหมู่: {$servicesWithCategory} รายการ\n";
    echo "บริการที่ไม่มีหมวดหมู่: {$servicesWithoutCategory} รายการ\n";

    if ($servicesWithCategory > 0) {
        echo "\n⚠️  พบบริการที่อ้างอิงหมวดหมู่ {$servicesWithCategory} รายการ\n";
        echo "จะต้องลบการอ้างอิงก่อนลบตาราง\n";
        
        $services = DB::table('services')->whereNotNull('category_id')->get(['id', 'title', 'category_id']);
        foreach ($services as $service) {
            echo "- บริการ ID: {$service->id} | {$service->title} | หมวดหมู่ ID: {$service->category_id}\n";
        }
    }

    echo "\n❓ คุณต้องการดำเนินการลบตาราง service_categories หรือไม่?\n";
    echo "   (การลบจะรวมถึงการลบการอ้างอิงในตาราง services ด้วย)\n";
    echo "   พิมพ์ 'YES' เพื่อยืนยัน หรือ 'NO' เพื่อยกเลิก: ";
    
    $handle = fopen("php://stdin", "r");
    $confirmation = trim(fgets($handle));
    fclose($handle);

    if (strtoupper($confirmation) !== 'YES') {
        echo "\n❌ การลบตารางถูกยกเลิก\n";
        exit(0);
    }

    echo "\n🚀 เริ่มดำเนินการลบ...\n";
    echo "----------------------------------------\n";

    // ขั้นตอนที่ 1: ลบการอ้างอิงในตาราง services
    if ($servicesWithCategory > 0) {
        echo "1. ลบการอ้างอิงหมวดหมู่ในตาราง services...\n";
        DB::table('services')->whereNotNull('category_id')->update(['category_id' => null]);
        echo "✅ ลบการอ้างอิงหมวดหมู่ใน {$servicesWithCategory} บริการแล้ว\n";
    }

    // ขั้นตอนที่ 2: สำรองข้อมูลตาราง service_categories
    if ($categoryCount > 0) {
        echo "2. สำรองข้อมูลตาราง service_categories...\n";
        
        $backupDir = 'backups';
        if (!is_dir($backupDir)) {
            mkdir($backupDir, 0755, true);
        }
        
        $timestamp = date('Y-m-d_H-i-s');
        $backupFile = $backupDir . '/service_categories_backup_' . $timestamp . '.sql';
        
        $backupContent = "-- สำรองข้อมูลตาราง service_categories\n";
        $backupContent .= "-- สร้างเมื่อ: " . date('Y-m-d H:i:s') . "\n\n";
        
        // สำรองโครงสร้างตาราง
        $createTableResult = DB::select("SHOW CREATE TABLE `service_categories`");
        $createTableSQL = $createTableResult[0]->{'Create Table'};
        $backupContent .= "DROP TABLE IF EXISTS `service_categories`;\n";
        $backupContent .= $createTableSQL . ";\n\n";
        
        // สำรองข้อมูล
        $categories = DB::table('service_categories')->get();
        if ($categories->count() > 0) {
            $backupContent .= "INSERT INTO `service_categories` (`id`, `name`, `description`, `icon`, `color`, `is_active`, `sort_order`, `created_at`, `updated_at`) VALUES\n";
            
            $values = [];
            foreach ($categories as $category) {
                $values[] = sprintf("(%d, '%s', %s, %s, '%s', %d, %d, '%s', '%s')",
                    $category->id,
                    addslashes($category->name),
                    $category->description ? "'" . addslashes($category->description) . "'" : 'NULL',
                    $category->icon ? "'" . addslashes($category->icon) . "'" : 'NULL',
                    addslashes($category->color),
                    $category->is_active ? 1 : 0,
                    $category->sort_order,
                    $category->created_at,
                    $category->updated_at
                );
            }
            
            $backupContent .= implode(",\n", $values) . ";\n\n";
        }
        
        file_put_contents($backupFile, $backupContent);
        echo "✅ สำรองข้อมูลไว้ที่: {$backupFile}\n";
    }

    // ขั้นตอนที่ 3: ลบตาราง service_categories
    echo "3. ลบตาราง service_categories...\n";
    Schema::dropIfExists('service_categories');
    echo "✅ ลบตาราง service_categories สำเร็จ\n";

    // ขั้นตอนที่ 4: ลบ column category_id จากตาราง services
    echo "4. ลบ column category_id จากตาราง services...\n";
    if (Schema::hasColumn('services', 'category_id')) {
        Schema::table('services', function ($table) {
            $table->dropColumn('category_id');
        });
        echo "✅ ลบ column category_id จากตาราง services สำเร็จ\n";
    } else {
        echo "⏭️  column category_id ไม่มีอยู่ในตาราง services\n";
    }

    echo "\n🎉 การลบตารางเสร็จสิ้น!\n";
    echo "----------------------------------------\n";
    echo "✅ ลบตาราง service_categories แล้ว\n";
    echo "✅ ลบการอ้างอิงในตาราง services แล้ว\n";
    echo "✅ ลบ column category_id จากตาราง services แล้ว\n";
    if ($categoryCount > 0) {
        echo "✅ สำรองข้อมูลไว้แล้ว\n";
    }

    echo "\n📝 สิ่งที่ควรทำต่อไป:\n";
    echo "1. ลบ Model ServiceCategory.php\n";
    echo "2. ลบการ import ServiceCategory ใน Controllers\n";
    echo "3. ลบการใช้งาน category ใน Views\n";
    echo "4. ลบ migration files ที่เกี่ยวข้อง (ถ้าต้องการ)\n";
    echo "5. ทดสอบเว็บไซต์ว่ายังทำงานปกติ\n\n";

    // แสดงตารางที่เหลือ
    echo "📋 ตารางที่เหลือในฐานข้อมูล:\n";
    echo "----------------------------------------\n";
    $databaseName = config('database.connections.mysql.database');
    $remainingTables = DB::select("SHOW TABLES");
    $tableColumn = "Tables_in_{$databaseName}";
    
    foreach ($remainingTables as $table) {
        $tableName = $table->$tableColumn;
        $count = DB::table($tableName)->count();
        echo "   - {$tableName} ({$count} รายการ)\n";
    }

} catch (Exception $e) {
    echo "❌ เกิดข้อผิดพลาด: " . $e->getMessage() . "\n";
    echo "📍 ไฟล์: " . $e->getFile() . " บรรทัด: " . $e->getLine() . "\n\n";
}

echo "\n========================================\n";
echo "   การดำเนินการเสร็จสิ้น\n";
echo "========================================\n";
?>
