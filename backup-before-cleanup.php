<?php
/**
 * สำรองข้อมูลก่อนลบตารางที่ไม่ได้ใช้งาน
 * สคริปต์นี้จะสำรองข้อมูลเฉพาะตารางที่ไม่ได้ใช้งานก่อนลบ
 */

require_once __DIR__ . '/vendor/autoload.php';

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

// Bootstrap Laravel
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

echo "========================================\n";
echo "   สำรองข้อมูลก่อนลบตารางที่ไม่ได้ใช้\n";
echo "========================================\n\n";

// ตารางที่ไม่ได้ใช้งาน
$unusedTables = [
    'password_resets',
    'failed_jobs', 
    'personal_access_tokens',
    'page_backgrounds',
    'page_background_images'
];

try {
    // ตรวจสอบการเชื่อมต่อฐานข้อมูล
    echo "🔍 ตรวจสอบการเชื่อมต่อฐานข้อมูล...\n";
    DB::connection()->getPdo();
    echo "✅ การเชื่อมต่อฐานข้อมูล: สำเร็จ\n\n";

    // สร้างชื่อไฟล์สำรอง
    $backupDir = 'backups';
    if (!is_dir($backupDir)) {
        mkdir($backupDir, 0755, true);
        echo "📁 สร้างโฟลเดอร์ backups\n";
    }

    $timestamp = date('Y-m-d_H-i-s');
    $backupFile = $backupDir . '/unused_tables_backup_' . $timestamp . '.sql';

    echo "💾 เริ่มสำรองข้อมูลตารางที่ไม่ได้ใช้งาน...\n";
    echo "📄 ไฟล์สำรอง: {$backupFile}\n\n";

    $backupContent = "-- สำรองข้อมูลตารางที่ไม่ได้ใช้งาน\n";
    $backupContent .= "-- สร้างเมื่อ: " . date('Y-m-d H:i:s') . "\n";
    $backupContent .= "-- ฐานข้อมูล: " . config('database.connections.mysql.database') . "\n\n";

    $backedUpTables = 0;
    $totalRecords = 0;

    foreach ($unusedTables as $tableName) {
        try {
            if (Schema::hasTable($tableName)) {
                echo "📋 สำรองตาราง: {$tableName}";
                
                // นับจำนวนข้อมูล
                $recordCount = DB::table($tableName)->count();
                echo " ({$recordCount} รายการ)\n";

                // เพิ่ม comment ในไฟล์สำรอง
                $backupContent .= "-- ตาราง: {$tableName} ({$recordCount} รายการ)\n";
                $backupContent .= "-- ========================================\n\n";

                if ($recordCount > 0) {
                    // สำรองโครงสร้างตาราง
                    $createTableResult = DB::select("SHOW CREATE TABLE `{$tableName}`");
                    $createTableSQL = $createTableResult[0]->{'Create Table'};
                    
                    $backupContent .= "-- โครงสร้างตาราง {$tableName}\n";
                    $backupContent .= "DROP TABLE IF EXISTS `{$tableName}`;\n";
                    $backupContent .= $createTableSQL . ";\n\n";

                    // สำรองข้อมูล
                    $backupContent .= "-- ข้อมูลในตาราง {$tableName}\n";
                    $records = DB::table($tableName)->get();
                    
                    if ($records->count() > 0) {
                        $columns = array_keys((array)$records->first());
                        $columnsList = '`' . implode('`, `', $columns) . '`';
                        
                        $backupContent .= "INSERT INTO `{$tableName}` ({$columnsList}) VALUES\n";
                        
                        $values = [];
                        foreach ($records as $record) {
                            $recordArray = (array)$record;
                            $escapedValues = [];
                            foreach ($recordArray as $value) {
                                if ($value === null) {
                                    $escapedValues[] = 'NULL';
                                } else {
                                    $escapedValues[] = "'" . addslashes($value) . "'";
                                }
                            }
                            $values[] = '(' . implode(', ', $escapedValues) . ')';
                        }
                        
                        $backupContent .= implode(",\n", $values) . ";\n\n";
                    }
                } else {
                    // ถ้าไม่มีข้อมูล ก็สำรองแค่โครงสร้าง
                    $createTableResult = DB::select("SHOW CREATE TABLE `{$tableName}`");
                    $createTableSQL = $createTableResult[0]->{'Create Table'};
                    
                    $backupContent .= "-- โครงสร้างตาราง {$tableName} (ไม่มีข้อมูล)\n";
                    $backupContent .= "DROP TABLE IF EXISTS `{$tableName}`;\n";
                    $backupContent .= $createTableSQL . ";\n\n";
                }

                $backedUpTables++;
                $totalRecords += $recordCount;
                
            } else {
                echo "⏭️  ข้าม: {$tableName} - ไม่พบตารางนี้\n";
            }
        } catch (Exception $e) {
            echo "❌ ไม่สามารถสำรองตาราง {$tableName}: " . $e->getMessage() . "\n";
        }
    }

    // เขียนไฟล์สำรอง
    if ($backedUpTables > 0) {
        file_put_contents($backupFile, $backupContent);
        
        echo "\n✅ สำรองข้อมูลเสร็จสิ้น!\n";
        echo "----------------------------------------\n";
        echo "📄 ไฟล์สำรอง: {$backupFile}\n";
        echo "📊 ตารางที่สำรอง: {$backedUpTables} ตาราง\n";
        echo "📈 รวมข้อมูล: {$totalRecords} รายการ\n";
        echo "💾 ขนาดไฟล์: " . formatBytes(filesize($backupFile)) . "\n\n";

        echo "📋 รายละเอียดการสำรอง:\n";
        echo "----------------------------------------\n";
        foreach ($unusedTables as $tableName) {
            if (Schema::hasTable($tableName)) {
                $count = DB::table($tableName)->count();
                echo "✅ {$tableName}: {$count} รายการ\n";
            } else {
                echo "⏭️  {$tableName}: ไม่พบตาราง\n";
            }
        }

        echo "\n💡 คำแนะนำ:\n";
        echo "----------------------------------------\n";
        echo "1. ตรวจสอบไฟล์สำรองที่ {$backupFile}\n";
        echo "2. เก็บไฟล์สำรองไว้ในที่ปลอดภัย\n";
        echo "3. ตอนนี้คุณสามารถรันสคริปต์ remove-unused-tables.php ได้แล้ว\n";
        echo "4. หากต้องการกู้คืน ใช้คำสั่ง: mysql -u username -p database_name < {$backupFile}\n\n";

        echo "🔧 วิธีกู้คืนข้อมูล (ถ้าจำเป็น):\n";
        $username = config('database.connections.mysql.username');
        $database = config('database.connections.mysql.database');
        echo "mysql -u {$username} -p {$database} < {$backupFile}\n\n";

    } else {
        echo "\n❌ ไม่มีตารางที่ต้องสำรอง\n";
        echo "ตารางที่ระบุไม่มีอยู่ในฐานข้อมูล\n\n";
    }

} catch (Exception $e) {
    echo "❌ เกิดข้อผิดพลาด: " . $e->getMessage() . "\n";
    echo "📍 ไฟล์: " . $e->getFile() . " บรรทัด: " . $e->getLine() . "\n\n";
    
    echo "🔧 วิธีแก้ไข:\n";
    echo "1. ตรวจสอบการตั้งค่าฐานข้อมูลในไฟล์ .env\n";
    echo "2. ตรวจสอบว่า MySQL Server ทำงานอยู่\n";
    echo "3. ตรวจสอบสิทธิ์การเขียนไฟล์ในโฟลเดอร์ backups\n";
}

/**
 * แปลงขนาดไฟล์เป็นหน่วยที่อ่านง่าย
 */
function formatBytes($size, $precision = 2) {
    $units = array('B', 'KB', 'MB', 'GB', 'TB');
    
    for ($i = 0; $size > 1024 && $i < count($units) - 1; $i++) {
        $size /= 1024;
    }
    
    return round($size, $precision) . ' ' . $units[$i];
}

echo "\n========================================\n";
echo "   การสำรองข้อมูลเสร็จสิ้น\n";
echo "========================================\n";
?>
