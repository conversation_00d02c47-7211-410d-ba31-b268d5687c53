<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Activity;
use App\Models\ActivityImage;

class ActivityImageSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get all activities that don't have images yet
        $activities = Activity::whereDoesntHave('images')->get();

        foreach ($activities as $activity) {
            // Create 2-4 sample images for each activity
            $imageCount = rand(2, 4);
            
            for ($i = 1; $i <= $imageCount; $i++) {
                ActivityImage::create([
                    'activity_id' => $activity->id,
                    'image_path' => $activity->image, // Use the existing image path as fallback
                    'alt_text' => $activity->title,
                    'description' => $activity->title . ' - รูปที่ ' . $i,
                    'is_cover' => $i === 1, // First image is cover
                    'sort_order' => $i
                ]);
            }
        }

        $this->command->info('✅ สร้าง ActivityImage สำหรับ Activities ที่ยังไม่มีรูปภาพเรียบร้อยแล้ว!');
        $this->command->info('📊 จำนวน Activities ที่ได้รับการเพิ่มรูปภาพ: ' . $activities->count());
    }
}
