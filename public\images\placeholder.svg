<svg width="400" height="300" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="grad1" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#2c3e50;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#34495e;stop-opacity:1" />
    </linearGradient>
  </defs>
  <rect width="400" height="300" fill="url(#grad1)" />
  <text x="200" y="140" font-family="Arial, sans-serif" font-size="18" fill="white" text-anchor="middle">ไม่มีรูปภาพ</text>
  <text x="200" y="170" font-family="Arial, sans-serif" font-size="14" fill="#bdc3c7" text-anchor="middle">No Image Available</text>
  <circle cx="200" cy="120" r="20" fill="none" stroke="white" stroke-width="2"/>
  <circle cx="200" cy="120" r="8" fill="white"/>
</svg>
