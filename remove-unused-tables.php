<?php
/**
 * ลบตารางที่ไม่ได้ใช้งานในฐานข้อมูล
 * สคริปต์นี้จะลบตารางที่ไม่ได้ใช้งานจริงในเว็บไซต์
 * ⚠️ ระวัง: การลบตารางจะไม่สามารถกู้คืนได้ กรุณาสำรองข้อมูลก่อน
 */

require_once __DIR__ . '/vendor/autoload.php';

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

// Bootstrap Laravel
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

echo "========================================\n";
echo "   ลบตารางที่ไม่ได้ใช้งาน\n";
echo "========================================\n\n";

// ตารางที่ไม่ได้ใช้งานและสามารถลบได้อย่างปลอดภัย
$unusedTables = [
    'password_resets' => 'ตารางรีเซ็ตรหัสผ่าน - ไม่มีฟีเจอร์รีเซ็ตรหัสผ่าน',
    'failed_jobs' => 'ตาราง Queue Jobs ที่ล้มเหลว - ไม่ได้ใช้ระบบ Queue',
    'personal_access_tokens' => 'ตาราง API Tokens - ไม่ได้ใช้ระบบ API',
    'page_backgrounds' => 'ตารางพื้นหลังหน้าเว็บ - ใช้ระบบ Banner แทน',
    'page_background_images' => 'ตารางรูปภาพพื้นหลัง - ใช้ระบบ Banner แทน'
];

try {
    // ตรวจสอบการเชื่อมต่อฐานข้อมูล
    echo "🔍 ตรวจสอบการเชื่อมต่อฐานข้อมูล...\n";
    DB::connection()->getPdo();
    echo "✅ การเชื่อมต่อฐานข้อมูล: สำเร็จ\n\n";

    // แสดงคำเตือน
    echo "⚠️  คำเตือนสำคัญ!\n";
    echo "========================================\n";
    echo "การลบตารางจะไม่สามารถกู้คืนได้!\n";
    echo "กรุณาสำรองข้อมูลก่อนดำเนินการ\n\n";

    // แสดงรายการตารางที่จะลบ
    echo "📋 ตารางที่จะลบ:\n";
    echo "----------------------------------------\n";
    foreach ($unusedTables as $tableName => $description) {
        if (Schema::hasTable($tableName)) {
            $count = DB::table($tableName)->count();
            echo "🗑️  {$tableName} ({$count} รายการ)\n";
            echo "    📝 {$description}\n\n";
        } else {
            echo "❌ {$tableName} - ไม่พบตารางนี้\n\n";
        }
    }

    // ขอการยืนยันจากผู้ใช้
    echo "❓ คุณต้องการดำเนินการลบตารางเหล่านี้หรือไม่?\n";
    echo "   พิมพ์ 'YES' เพื่อยืนยัน หรือ 'NO' เพื่อยกเลิก: ";
    
    $handle = fopen("php://stdin", "r");
    $confirmation = trim(fgets($handle));
    fclose($handle);

    if (strtoupper($confirmation) !== 'YES') {
        echo "\n❌ การลบตารางถูกยกเลิก\n";
        echo "✅ ข้อมูลของคุณปลอดภัย\n\n";
        exit(0);
    }

    echo "\n🚀 เริ่มดำเนินการลบตาราง...\n";
    echo "----------------------------------------\n";

    $deletedCount = 0;
    $skippedCount = 0;

    foreach ($unusedTables as $tableName => $description) {
        try {
            if (Schema::hasTable($tableName)) {
                // ตรวจสอบจำนวนข้อมูลก่อนลบ
                $recordCount = DB::table($tableName)->count();
                
                // ลบตาราง
                Schema::dropIfExists($tableName);
                
                echo "✅ ลบตาราง '{$tableName}' สำเร็จ ({$recordCount} รายการ)\n";
                $deletedCount++;
            } else {
                echo "⏭️  ข้าม '{$tableName}' - ไม่พบตารางนี้\n";
                $skippedCount++;
            }
        } catch (Exception $e) {
            echo "❌ ไม่สามารถลบตาราง '{$tableName}': " . $e->getMessage() . "\n";
            $skippedCount++;
        }
    }

    echo "\n📊 สรุปผลการดำเนินการ:\n";
    echo "----------------------------------------\n";
    echo "✅ ลบตารางสำเร็จ: {$deletedCount} ตาราง\n";
    echo "⏭️  ข้ามตาราง: {$skippedCount} ตาราง\n";
    echo "📈 รวมทั้งหมด: " . count($unusedTables) . " ตาราง\n\n";

    if ($deletedCount > 0) {
        echo "🎉 การลบตารางเสร็จสิ้น!\n";
        echo "----------------------------------------\n";
        echo "✅ ลบตารางที่ไม่ได้ใช้งานแล้ว {$deletedCount} ตาราง\n";
        echo "💾 ฐานข้อมูลของคุณตอนนี้สะอาดขึ้นแล้ว\n";
        echo "📉 ขนาดฐานข้อมูลลดลง\n\n";

        echo "📝 สิ่งที่ควรทำต่อไป:\n";
        echo "1. ตรวจสอบว่าเว็บไซต์ยังทำงานปกติ\n";
        echo "2. ทดสอบฟีเจอร์ต่างๆ ของเว็บไซต์\n";
        echo "3. สำรองข้อมูลใหม่หลังจากการเปลี่ยนแปลง\n\n";

        // แสดงคำสั่ง SQL สำหรับสำรองข้อมูล
        $databaseName = config('database.connections.mysql.database');
        $username = config('database.connections.mysql.username');
        
        echo "💾 คำสั่งสำรองข้อมูลใหม่:\n";
        echo "mysqldump -u {$username} -p {$databaseName} > backup_after_cleanup_" . date('Y-m-d_H-i-s') . ".sql\n\n";
    } else {
        echo "ℹ️  ไม่มีตารางที่ถูกลบ\n";
        echo "อาจเป็นเพราะตารางเหล่านั้นไม่มีอยู่ในฐานข้อมูล\n\n";
    }

    // ตรวจสอบตารางที่เหลือ
    echo "📋 ตรวจสอบตารางที่เหลือในฐานข้อมูล:\n";
    echo "----------------------------------------\n";
    
    $databaseName = config('database.connections.mysql.database');
    $remainingTables = DB::select("SHOW TABLES");
    $tableColumn = "Tables_in_{$databaseName}";
    
    echo "📊 ตารางที่เหลือ: " . count($remainingTables) . " ตาราง\n";
    foreach ($remainingTables as $table) {
        $tableName = $table->$tableColumn;
        $count = DB::table($tableName)->count();
        echo "   - {$tableName} ({$count} รายการ)\n";
    }

} catch (Exception $e) {
    echo "❌ เกิดข้อผิดพลาด: " . $e->getMessage() . "\n";
    echo "📍 ไฟล์: " . $e->getFile() . " บรรทัด: " . $e->getLine() . "\n\n";
    
    echo "🔧 วิธีแก้ไข:\n";
    echo "1. ตรวจสอบการตั้งค่าฐานข้อมูลในไฟล์ .env\n";
    echo "2. ตรวจสอบว่า MySQL Server ทำงานอยู่\n";
    echo "3. ตรวจสอบสิทธิ์การเข้าถึงฐานข้อมูล\n";
    echo "4. สำรองข้อมูลก่อนลองใหม่\n";
}

echo "\n========================================\n";
echo "   การดำเนินการเสร็จสิ้น\n";
echo "========================================\n";
?>
