<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>สร้างโลโก้ผึ้งทอง - Placeholder</title>
    <style>
        body {
            font-family: 'Se<PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
        }

        .container {
            background: white;
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            text-align: center;
            max-width: 600px;
        }

        .bee-icon {
            width: 300px;
            height: 300px;
            margin: 20px auto;
            background: linear-gradient(45deg, #FFD700, #FFA500);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 120px;
            box-shadow: 0 10px 30px rgba(255, 215, 0, 0.3);
            animation: float 3s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }

        h1 {
            color: #2c3e50;
            margin-bottom: 20px;
        }

        .instructions {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            text-align: left;
        }

        .download-btn {
            background: linear-gradient(135deg, #FFD700, #FFA500);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            margin: 10px;
            transition: all 0.3s ease;
        }

        .download-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(255, 165, 0, 0.4);
        }

        .note {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 15px;
            margin: 20px 0;
            color: #856404;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🐝 โลโก้ผึ้งทอง - Placeholder</h1>
        
        <div class="bee-icon">
            🐝
        </div>

        <div class="note">
            <strong>📌 หมายเหตุ:</strong> นี่เป็นเพียงตัวอย่าง emoji ผึ้ง<br>
            คุณต้องแทนที่ด้วยรูปโลโก้ผึ้งจริงของคุณ
        </div>

        <div class="instructions">
            <h3>🔧 วิธีการใช้งาน:</h3>
            <ol>
                <li><strong>เตรียมรูปโลโก้ผึ้ง</strong> ในรูปแบบ PNG พื้นหลังโปร่งใส</li>
                <li><strong>ตั้งชื่อไฟล์</strong> เป็น <code>bee-logo.png</code></li>
                <li><strong>วางไฟล์</strong> ในโฟลเดอร์ <code>public/images/</code></li>
                <li><strong>รีเฟรชหน้าเว็บ</strong> เพื่อดูผลลัพธ์</li>
            </ol>
        </div>

        <div class="instructions">
            <h3>🎨 คำแนะนำการออกแบบ:</h3>
            <ul>
                <li><strong>ขนาด:</strong> สูงสูงสุด 300px</li>
                <li><strong>รูปแบบ:</strong> PNG พื้นหลังโปร่งใส</li>
                <li><strong>สี:</strong> สีทอง, น้ำตาล, หรือสีที่เข้ากับธีม</li>
                <li><strong>คุณภาพ:</strong> ความละเอียดสูง เพื่อความคมชัด</li>
            </ul>
        </div>

        <div class="instructions">
            <h3>✨ เอฟเฟกต์พิเศษที่จะได้:</h3>
            <ul>
                <li>🌊 <strong>แอนิเมชั่นลอย</strong> - ขึ้นลงอย่างนุ่มนวล</li>
                <li>🎯 <strong>Hover Effect</strong> - ขยายและหมุนเมื่อเอาเมาส์ชี้</li>
                <li>💫 <strong>Drop Shadow</strong> - เงาตกกระทบสวยงาม</li>
                <li>🌟 <strong>เรืองแสง</strong> - เอฟเฟกต์เรืองแสงสีทอง</li>
            </ul>
        </div>

        <button class="download-btn" onclick="window.open('https://www.flaticon.com/search?word=bee&type=icon', '_blank')">
            🔍 หาไอคอนผึ้งฟรี
        </button>

        <button class="download-btn" onclick="window.open('https://www.canva.com/search/templates?q=bee%20logo', '_blank')">
            🎨 สร้างโลโก้ผึ้งใน Canva
        </button>

        <div class="note">
            <strong>💡 เคล็ดลับ:</strong> หากต้องการเปลี่ยนชื่อไฟล์ ให้แก้ไขใน code ที่ไฟล์ 
            <code>resources/views/frontend/home.blade.php</code> ด้วย
        </div>
    </div>
</body>
</html>
