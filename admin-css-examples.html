<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Phuyai Prajak Service Shop Admin - CSS Examples</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=Kanit:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="public/css/admin-custom.css" rel="stylesheet">
</head>
<body>
    <div class="container-fluid p-4">
        <h1 class="text-gradient mb-4">Phuyai Prajak Service Shop Admin - CSS Examples</h1>
        
        <!-- Color Palette -->
        <div class="row mb-5">
            <div class="col-12">
                <h2 class="mb-3">Color Palette</h2>
                <div class="row g-3">
                    <div class="col-md-2">
                        <div class="card text-center">
                            <div class="card-body" style="background: var(--primary-color); color: white;">
                                <h6>Primary</h6>
                                <small>#3B82F6</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="card text-center">
                            <div class="card-body" style="background: var(--secondary-color); color: white;">
                                <h6>Secondary</h6>
                                <small>#6366F1</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="card text-center">
                            <div class="card-body" style="background: var(--success-color); color: white;">
                                <h6>Success</h6>
                                <small>#10B981</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="card text-center">
                            <div class="card-body" style="background: var(--warning-color); color: white;">
                                <h6>Warning</h6>
                                <small>#F59E0B</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="card text-center">
                            <div class="card-body" style="background: var(--danger-color); color: white;">
                                <h6>Danger</h6>
                                <small>#EF4444</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="card text-center">
                            <div class="card-body" style="background: var(--info-color); color: white;">
                                <h6>Info</h6>
                                <small>#06B6D4</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Buttons -->
        <div class="row mb-5">
            <div class="col-12">
                <h2 class="mb-3">Buttons</h2>
                <div class="d-flex flex-wrap gap-3">
                    <button class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>Primary Button
                    </button>
                    <button class="btn btn-success">
                        <i class="fas fa-check me-2"></i>Success Button
                    </button>
                    <button class="btn btn-warning">
                        <i class="fas fa-exclamation me-2"></i>Warning Button
                    </button>
                    <button class="btn btn-info">
                        <i class="fas fa-info me-2"></i>Info Button
                    </button>
                    <button class="btn btn-outline-primary">
                        <i class="fas fa-edit me-2"></i>Outline Primary
                    </button>
                    <button class="btn btn-outline-secondary">
                        <i class="fas fa-cog me-2"></i>Outline Secondary
                    </button>
                </div>
            </div>
        </div>

        <!-- Cards -->
        <div class="row mb-5">
            <div class="col-12">
                <h2 class="mb-3">Cards</h2>
                <div class="row g-4">
                    <div class="col-md-4">
                        <div class="card card-hover">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="fas fa-chart-bar me-2 text-primary"></i>Regular Card
                                </h5>
                            </div>
                            <div class="card-body">
                                <p class="card-text">This is a regular card with hover effect.</p>
                                <button class="btn btn-primary">Action</button>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card stats-card pulse-hover">
                            <div class="card-body text-white">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <h6 class="mb-1 opacity-90">Stats Card</h6>
                                        <h2 class="mb-0 fw-bold">1,234</h2>
                                    </div>
                                    <div class="text-white-50">
                                        <i class="fas fa-users fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card bg-success text-white pulse-hover shadow-success">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <h6 class="mb-1 opacity-90">Success Card</h6>
                                        <h2 class="mb-0 fw-bold">567</h2>
                                    </div>
                                    <div class="text-white-50">
                                        <i class="fas fa-check-circle fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Alerts -->
        <div class="row mb-5">
            <div class="col-12">
                <h2 class="mb-3">Alerts</h2>
                <div class="alert alert-success">
                    <i class="fas fa-check-circle me-2"></i>
                    This is a success alert with enhanced styling!
                </div>
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    This is a warning alert with enhanced styling!
                </div>
                <div class="alert alert-danger">
                    <i class="fas fa-times-circle me-2"></i>
                    This is a danger alert with enhanced styling!
                </div>
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    This is an info alert with enhanced styling!
                </div>
            </div>
        </div>

        <!-- Form Controls -->
        <div class="row mb-5">
            <div class="col-12">
                <h2 class="mb-3">Form Controls</h2>
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">Text Input</label>
                            <input type="text" class="form-control" placeholder="Enter text here...">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Select Dropdown</label>
                            <select class="form-select">
                                <option>Choose option...</option>
                                <option>Option 1</option>
                                <option>Option 2</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">Textarea</label>
                            <textarea class="form-control" rows="4" placeholder="Enter your message..."></textarea>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Badges -->
        <div class="row mb-5">
            <div class="col-12">
                <h2 class="mb-3">Badges</h2>
                <div class="d-flex flex-wrap gap-2">
                    <span class="badge bg-primary">Primary Badge</span>
                    <span class="badge bg-success">Success Badge</span>
                    <span class="badge bg-warning">Warning Badge</span>
                    <span class="badge bg-danger">Danger Badge</span>
                    <span class="badge bg-info">Info Badge</span>
                </div>
            </div>
        </div>

        <!-- Utility Classes -->
        <div class="row mb-5">
            <div class="col-12">
                <h2 class="mb-3">Utility Classes</h2>
                <div class="row g-3">
                    <div class="col-md-4">
                        <div class="card shadow-primary">
                            <div class="card-body text-center">
                                <h5 class="text-gradient">Gradient Text</h5>
                                <p>Card with primary shadow</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card shadow-success">
                            <div class="card-body text-center">
                                <h5>Success Shadow</h5>
                                <p>Card with success shadow</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card shadow-warning">
                            <div class="card-body text-center">
                                <h5>Warning Shadow</h5>
                                <p>Card with warning shadow</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Add ripple effect to buttons
        document.querySelectorAll('.btn').forEach(button => {
            button.addEventListener('click', function(e) {
                const ripple = document.createElement('span');
                const rect = this.getBoundingClientRect();
                const size = Math.max(rect.width, rect.height);
                const x = e.clientX - rect.left - size / 2;
                const y = e.clientY - rect.top - size / 2;
                
                ripple.style.width = ripple.style.height = size + 'px';
                ripple.style.left = x + 'px';
                ripple.style.top = y + 'px';
                ripple.classList.add('ripple');
                
                this.appendChild(ripple);
                
                setTimeout(() => {
                    ripple.remove();
                }, 600);
            });
        });
    </script>
</body>
</html>
