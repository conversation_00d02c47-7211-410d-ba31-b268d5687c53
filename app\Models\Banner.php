<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Banner extends Model
{
    use HasFactory;

    protected $fillable = [
        'title',
        'description',
        'image_path',
        'display_pages',
        'is_active',
        'sort_order'
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'display_pages' => 'array',
    ];

    // Scope สำหรับแบนเนอร์ที่เปิดใช้งาน
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    // Scope สำหรับเรียงลำดับ
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order', 'asc')->orderBy('created_at', 'asc');
    }



    // Scope สำหรับกรองตามหน้า
    public function scopeForPage($query, $page)
    {
        return $query->where(function($q) use ($page) {
            $q->whereNull('display_pages')
              ->orWhereJsonContains('display_pages', $page);
        });
    }

    // ดึงชื่อหน้าที่แสดง
    public function getDisplayPagesNameAttribute()
    {
        if (!$this->display_pages || empty($this->display_pages)) {
            return 'ทุกหน้า';
        }

        $pageNames = [
            'home' => 'หน้าหลัก',
            'services' => 'บริการ',
            'packages' => 'แพ็คเกจ',
            'activities' => 'ผลงาน',
            'contact' => 'ติดต่อเรา'
        ];

        $names = [];
        foreach ($this->display_pages as $page) {
            $names[] = $pageNames[$page] ?? $page;
        }

        return implode(', ', $names);
    }
}
