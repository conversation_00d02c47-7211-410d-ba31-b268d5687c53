<?php
/**
 * Database Sync Checker
 * ตรวจสอบการซิงค์ข้อมูลระหว่างเว็บไซต์และฐานข้อมูล
 */

require_once 'vendor/autoload.php';

// โหลด Laravel Application
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

echo "========================================\n";
echo "   Database Sync Checker\n";
echo "========================================\n\n";

try {
    // ตรวจสอบการเชื่อมต่อฐานข้อมูล
    echo "🔍 ตรวจสอบการเชื่อมต่อฐานข้อมูล...\n";
    DB::connection()->getPdo();
    echo "✅ การเชื่อมต่อฐานข้อมูล: สำเร็จ\n\n";

    // ตรวจสอบข้อมูลในแต่ละตาราง
    $tables = [
        'services' => App\Models\Service::class,
        'packages' => App\Models\Package::class,
        'activities' => App\Models\Activity::class,
        'contacts' => App\Models\Contact::class,
        'site_settings' => App\Models\SiteSetting::class,
        'users' => App\Models\User::class,
    ];

    echo "📊 สถิติข้อมูลในฐานข้อมูล:\n";
    echo "----------------------------------------\n";

    foreach ($tables as $tableName => $modelClass) {
        try {
            $count = $modelClass::count();
            $latest = $modelClass::latest()->first();
            $latestDate = $latest ? $latest->created_at->format('Y-m-d H:i:s') : 'ไม่มีข้อมูล';
            
            echo sprintf("%-15s: %3d รายการ (ล่าสุด: %s)\n", 
                ucfirst($tableName), $count, $latestDate);
        } catch (Exception $e) {
            echo sprintf("%-15s: ❌ Error - %s\n", ucfirst($tableName), $e->getMessage());
        }
    }

    echo "\n";

    // ตรวจสอบข้อมูลที่แสดงในหน้าเว็บ
    echo "🌐 ตรวจสอบข้อมูลที่แสดงในหน้าเว็บ:\n";
    echo "----------------------------------------\n";

    // Services ที่แสดงในหน้าแรก
    $homeServices = App\Models\Service::active()
        ->orderBy('sort_order', 'asc')
        ->orderBy('created_at', 'asc')
        ->take(6)
        ->get();
    echo "Services ในหน้าแรก: " . $homeServices->count() . " รายการ\n";

    // Activities ที่แสดงในหน้าแรก
    $homeActivities = App\Models\Activity::with('images')
        ->active()
        ->inRandomOrder()
        ->take(4)
        ->get();
    echo "Activities ในหน้าแรก: " . $homeActivities->count() . " รายการ\n";

    // Site Settings
    $settings = App\Models\SiteSetting::all();
    echo "Site Settings: " . $settings->count() . " รายการ\n\n";

    // แสดงรายการ Services ล่าสุด
    echo "📋 Services ล่าสุด 5 รายการ:\n";
    echo "----------------------------------------\n";
    $recentServices = App\Models\Service::latest()->take(5)->get(['id', 'title', 'is_active', 'created_at']);
    foreach ($recentServices as $service) {
        $status = $service->is_active ? '🟢 Active' : '🔴 Inactive';
        echo sprintf("ID: %3d | %s | %s | %s\n", 
            $service->id, 
            $status,
            $service->created_at->format('Y-m-d H:i'),
            Str::limit($service->title, 40)
        );
    }

    echo "\n";

    // ตรวจสอบ Cache
    echo "💾 ตรวจสอบ Cache:\n";
    echo "----------------------------------------\n";
    
    $cacheDriver = config('cache.default');
    echo "Cache Driver: " . $cacheDriver . "\n";
    
    if ($cacheDriver === 'file') {
        $cachePath = storage_path('framework/cache/data');
        $cacheFiles = glob($cachePath . '/*');
        echo "Cache Files: " . count($cacheFiles) . " ไฟล์\n";
    }

    echo "\n";

    // คำแนะนำ
    echo "💡 คำแนะนำ:\n";
    echo "----------------------------------------\n";
    echo "1. หากข้อมูลในฐานข้อมูลไม่ตรงกับที่แสดงในเว็บ ให้ล้าง cache:\n";
    echo "   php artisan cache:clear\n";
    echo "   php artisan view:clear\n\n";
    echo "2. หากเพิ่มข้อมูลในเว็บแล้วไม่ปรากฏในฐานข้อมูล ตรวจสอบ:\n";
    echo "   - การตั้งค่าฐานข้อมูลในไฟล์ .env\n";
    echo "   - Log files ใน storage/logs/\n\n";
    echo "3. หากลบข้อมูลในฐานข้อมูลแล้วยังแสดงในเว็บ:\n";
    echo "   - ล้าง cache ทั้งหมด\n";
    echo "   - Hard refresh เบราว์เซอร์ (Ctrl+F5)\n\n";

} catch (Exception $e) {
    echo "❌ เกิดข้อผิดพลาด: " . $e->getMessage() . "\n";
    echo "📍 ไฟล์: " . $e->getFile() . " บรรทัด: " . $e->getLine() . "\n\n";
    
    echo "🔧 วิธีแก้ไข:\n";
    echo "1. ตรวจสอบการตั้งค่าฐานข้อมูลในไฟล์ .env\n";
    echo "2. ตรวจสอบว่า MySQL Server ทำงานอยู่\n";
    echo "3. ตรวจสอบว่าฐานข้อมูล 'phuyai_prajak_service_shop' มีอยู่จริง\n";
    echo "4. รัน: php artisan migrate:status\n";
}

echo "\n========================================\n";
echo "   การตรวจสอบเสร็จสิ้น\n";
echo "========================================\n";
?>
