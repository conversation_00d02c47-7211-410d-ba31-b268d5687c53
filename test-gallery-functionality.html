<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ทดสอบ Gallery Function</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .gallery-thumbnail {
            transition: all 0.3s ease;
            border: 2px solid transparent;
            cursor: pointer;
        }
        
        .gallery-thumbnail:hover {
            transform: scale(1.05);
            border-color: #0d6efd;
        }
        
        .active-thumbnail .gallery-thumbnail {
            border-color: #0d6efd !important;
            transform: scale(1.05) !important;
        }
        
        .modal-thumb {
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }
        
        .modal-thumb:hover {
            transform: scale(1.1);
            border-color: #0d6efd;
        }
        
        .modal-thumb.active {
            border-color: #0d6efd !important;
            transform: scale(1.1) !important;
        }
        
        .img-size-thumbnail {
            height: 120px;
        }
        
        .img-fit-contain {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
    </style>
</head>
<body>
    <div class="container py-5">
        <h1 class="text-center mb-5">ทดสอบ Gallery Function</h1>
        
        <!-- Main Image -->
        <div class="row mb-4">
            <div class="col-lg-8 mx-auto">
                <div class="text-center">
                    <img id="mainImage" 
                         src="https://via.placeholder.com/600x400/007bff/ffffff?text=Main+Image+1" 
                         class="img-fluid rounded shadow"
                         alt="Main Image"
                         style="cursor: pointer; max-height: 400px;">
                </div>
            </div>
        </div>
        
        <!-- Gallery Thumbnails -->
        <div class="row mb-4">
            <div class="col-12">
                <h5 class="mb-3">แกลเลอรี่รูปภาพ</h5>
                <div class="row g-2">
                    <div class="col-md-3 col-4">
                        <div class="gallery-image-container img-size-thumbnail">
                            <img src="https://via.placeholder.com/300x200/007bff/ffffff?text=Image+1"
                                 class="img-fit-contain gallery-thumbnail"
                                 alt="Image 1"
                                 data-image="https://via.placeholder.com/600x400/007bff/ffffff?text=Main+Image+1"
                                 data-caption="Image 1">
                        </div>
                    </div>
                    <div class="col-md-3 col-4">
                        <div class="gallery-image-container img-size-thumbnail">
                            <img src="https://via.placeholder.com/300x200/28a745/ffffff?text=Image+2"
                                 class="img-fit-contain gallery-thumbnail"
                                 alt="Image 2"
                                 data-image="https://via.placeholder.com/600x400/28a745/ffffff?text=Main+Image+2"
                                 data-caption="Image 2">
                        </div>
                    </div>
                    <div class="col-md-3 col-4">
                        <div class="gallery-image-container img-size-thumbnail">
                            <img src="https://via.placeholder.com/300x200/dc3545/ffffff?text=Image+3"
                                 class="img-fit-contain gallery-thumbnail"
                                 alt="Image 3"
                                 data-image="https://via.placeholder.com/600x400/dc3545/ffffff?text=Main+Image+3"
                                 data-caption="Image 3">
                        </div>
                    </div>
                    <div class="col-md-3 col-4">
                        <div class="gallery-image-container img-size-thumbnail">
                            <img src="https://via.placeholder.com/300x200/ffc107/000000?text=Image+4"
                                 class="img-fit-contain gallery-thumbnail"
                                 alt="Image 4"
                                 data-image="https://via.placeholder.com/600x400/ffc107/000000?text=Main+Image+4"
                                 data-caption="Image 4">
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Test Results -->
        <div class="row">
            <div class="col-12">
                <h3>ผลการทดสอบ:</h3>
                <div id="test-results" class="alert alert-info">
                    คลิกที่รูปภาพเพื่อทดสอบ Gallery
                </div>
            </div>
        </div>
    </div>

    <!-- Image Modal -->
    <div class="modal fade" id="imageModal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-xl modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Gallery Test</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body text-center p-0 position-relative">
                    <img src="https://via.placeholder.com/600x400/007bff/ffffff?text=Main+Image+1"
                         class="img-fluid w-100"
                         alt="Modal Image"
                         id="modalImage"
                         style="max-height: 80vh; object-fit: contain;">

                    <!-- Navigation arrows -->
                    <button class="btn btn-dark btn-sm position-absolute top-50 start-0 translate-middle-y ms-3"
                            id="prevImageBtn" style="z-index: 10;">
                        <i class="fas fa-chevron-left"></i>
                    </button>
                    <button class="btn btn-dark btn-sm position-absolute top-50 end-0 translate-middle-y me-3"
                            id="nextImageBtn" style="z-index: 10;">
                        <i class="fas fa-chevron-right"></i>
                    </button>
                </div>
                <div class="modal-footer justify-content-center">
                    <div class="d-flex gap-2 flex-wrap">
                        <div class="modal-thumb active"
                             style="width: 60px; height: 60px; cursor: pointer; border: 2px solid transparent; border-radius: 4px; overflow: hidden;"
                             data-index="0"
                             data-image="https://via.placeholder.com/600x400/007bff/ffffff?text=Main+Image+1"
                             data-alt="Image 1">
                            <img src="https://via.placeholder.com/300x200/007bff/ffffff?text=Image+1"
                                 class="img-thumbnail w-100 h-100"
                                 style="object-fit: cover;"
                                 alt="Image 1">
                        </div>
                        <div class="modal-thumb"
                             style="width: 60px; height: 60px; cursor: pointer; border: 2px solid transparent; border-radius: 4px; overflow: hidden;"
                             data-index="1"
                             data-image="https://via.placeholder.com/600x400/28a745/ffffff?text=Main+Image+2"
                             data-alt="Image 2">
                            <img src="https://via.placeholder.com/300x200/28a745/ffffff?text=Image+2"
                                 class="img-thumbnail w-100 h-100"
                                 style="object-fit: cover;"
                                 alt="Image 2">
                        </div>
                        <div class="modal-thumb"
                             style="width: 60px; height: 60px; cursor: pointer; border: 2px solid transparent; border-radius: 4px; overflow: hidden;"
                             data-index="2"
                             data-image="https://via.placeholder.com/600x400/dc3545/ffffff?text=Main+Image+3"
                             data-alt="Image 3">
                            <img src="https://via.placeholder.com/300x200/dc3545/ffffff?text=Image+3"
                                 class="img-thumbnail w-100 h-100"
                                 style="object-fit: cover;"
                                 alt="Image 3">
                        </div>
                        <div class="modal-thumb"
                             style="width: 60px; height: 60px; cursor: pointer; border: 2px solid transparent; border-radius: 4px; overflow: hidden;"
                             data-index="3"
                             data-image="https://via.placeholder.com/600x400/ffc107/000000?text=Main+Image+4"
                             data-alt="Image 4">
                            <img src="https://via.placeholder.com/300x200/ffc107/000000?text=Image+4"
                                 class="img-thumbnail w-100 h-100"
                                 style="object-fit: cover;"
                                 alt="Image 4">
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="js/stable-modal.js"></script>
    <script>
        // Test Gallery functionality
        document.addEventListener('DOMContentLoaded', function() {
            const resultsDiv = document.getElementById('test-results');
            
            // Test main image click
            const mainImage = document.getElementById('mainImage');
            if (mainImage) {
                mainImage.addEventListener('click', function() {
                    resultsDiv.innerHTML = '<strong>Main Image คลิก:</strong> เปิด Modal';
                    resultsDiv.className = 'alert alert-success';
                });
            }
            
            // Test gallery thumbnail clicks
            document.querySelectorAll('.gallery-thumbnail').forEach((thumb, index) => {
                thumb.addEventListener('click', function() {
                    resultsDiv.innerHTML = `<strong>Gallery Thumbnail ${index + 1} คลิก:</strong> เปลี่ยนรูปหลัก`;
                    resultsDiv.className = 'alert alert-warning';
                });
            });
            
            // Test modal thumbnail clicks
            document.querySelectorAll('.modal-thumb').forEach((thumb, index) => {
                thumb.addEventListener('click', function() {
                    resultsDiv.innerHTML = `<strong>Modal Thumbnail ${index + 1} คลิก:</strong> เปลี่ยนรูปใน Modal`;
                    resultsDiv.className = 'alert alert-info';
                });
            });
        });
    </script>
</body>
</html>
