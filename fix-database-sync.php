<?php
/**
 * Database Sync Fixer
 * แก้ไขปัญหาการซิงค์ข้อมูลระหว่างเว็บไซต์และฐานข้อมูล
 */

require_once 'vendor/autoload.php';

// โหลด Laravel Application
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

echo "========================================\n";
echo "   Database Sync Fixer\n";
echo "========================================\n\n";

function runCommand($command, $description) {
    echo "🔧 {$description}...\n";
    $output = [];
    $return_code = 0;
    exec("php artisan {$command} 2>&1", $output, $return_code);
    
    if ($return_code === 0) {
        echo "✅ สำเร็จ: " . implode("\n", $output) . "\n\n";
        return true;
    } else {
        echo "❌ ล้มเหลว: " . implode("\n", $output) . "\n\n";
        return false;
    }
}

try {
    // ขั้นตอนที่ 1: ล้าง Cache ทั้งหมด
    echo "📋 ขั้นตอนที่ 1: ล้าง Cache ทั้งหมด\n";
    echo "----------------------------------------\n";
    
    $cacheCommands = [
        'cache:clear' => 'ล้าง Application Cache',
        'view:clear' => 'ล้าง Compiled Views',
        'config:clear' => 'ล้าง Configuration Cache',
        'route:clear' => 'ล้าง Route Cache'
    ];
    
    foreach ($cacheCommands as $command => $description) {
        runCommand($command, $description);
    }

    // ขั้นตอนที่ 2: ตรวจสอบการเชื่อมต่อฐานข้อมูล
    echo "📋 ขั้นตอนที่ 2: ตรวจสอบการเชื่อมต่อฐานข้อมูล\n";
    echo "----------------------------------------\n";
    
    DB::connection()->getPdo();
    echo "✅ การเชื่อมต่อฐานข้อมูล: สำเร็จ\n\n";

    // ขั้นตอนที่ 3: ตรวจสอบ Migration Status
    echo "📋 ขั้นตอนที่ 3: ตรวจสอบ Migration Status\n";
    echo "----------------------------------------\n";
    runCommand('migrate:status', 'ตรวจสอบสถานะ Migration');

    // ขั้นตอนที่ 4: ทดสอบการเขียนข้อมูล
    echo "📋 ขั้นตอนที่ 4: ทดสอบการเขียนข้อมูล\n";
    echo "----------------------------------------\n";
    
    // สร้าง Service ทดสอบ
    $testService = App\Models\Service::create([
        'title' => 'ทดสอบการซิงค์ข้อมูล - ' . date('Y-m-d H:i:s'),
        'description' => 'บริการทดสอบสำหรับตรวจสอบการซิงค์ข้อมูล',
        'is_active' => true,
        'sort_order' => 999
    ]);
    
    echo "✅ สร้าง Service ทดสอบ ID: {$testService->id}\n";
    
    // ตรวจสอบว่าข้อมูลถูกบันทึกจริง
    $checkService = App\Models\Service::find($testService->id);
    if ($checkService) {
        echo "✅ ตรวจสอบข้อมูลในฐานข้อมูล: พบข้อมูล\n";
    } else {
        echo "❌ ตรวจสอบข้อมูลในฐานข้อมูล: ไม่พบข้อมูล\n";
    }
    
    // ลบข้อมูลทดสอบ
    $testService->delete();
    echo "🗑️ ลบข้อมูลทดสอบแล้ว\n\n";

    // ขั้นตอนที่ 5: ตรวจสอบ File Permissions
    echo "📋 ขั้นตอนที่ 5: ตรวจสอบ File Permissions\n";
    echo "----------------------------------------\n";
    
    $directories = [
        'storage/app',
        'storage/framework',
        'storage/logs',
        'bootstrap/cache'
    ];
    
    foreach ($directories as $dir) {
        if (is_writable($dir)) {
            echo "✅ {$dir}: สามารถเขียนได้\n";
        } else {
            echo "❌ {$dir}: ไม่สามารถเขียนได้\n";
        }
    }
    echo "\n";

    // ขั้นตอนที่ 6: ตรวจสอบ Environment
    echo "📋 ขั้นตอนที่ 6: ตรวจสอบ Environment\n";
    echo "----------------------------------------\n";
    
    echo "APP_ENV: " . env('APP_ENV') . "\n";
    echo "APP_DEBUG: " . (env('APP_DEBUG') ? 'true' : 'false') . "\n";
    echo "DB_CONNECTION: " . env('DB_CONNECTION') . "\n";
    echo "DB_DATABASE: " . env('DB_DATABASE') . "\n";
    echo "CACHE_DRIVER: " . env('CACHE_DRIVER') . "\n";
    echo "SESSION_DRIVER: " . env('SESSION_DRIVER') . "\n\n";

    // ขั้นตอนที่ 7: สถิติข้อมูลปัจจุบัน
    echo "📋 ขั้นตอนที่ 7: สถิติข้อมูลปัจจุบัน\n";
    echo "----------------------------------------\n";
    
    $stats = [
        'Services' => App\Models\Service::count(),
        'Active Services' => App\Models\Service::where('is_active', true)->count(),
        'Packages' => App\Models\Package::count(),
        'Activities' => App\Models\Activity::count(),
        'Contacts' => App\Models\Contact::count(),
        'Site Settings' => App\Models\SiteSetting::count(),
        'Users' => App\Models\User::count(),
    ];
    
    foreach ($stats as $name => $count) {
        echo sprintf("%-20s: %d รายการ\n", $name, $count);
    }
    echo "\n";

    // สรุปผลการแก้ไข
    echo "🎉 การแก้ไขเสร็จสิ้น!\n";
    echo "----------------------------------------\n";
    echo "✅ ล้าง Cache ทั้งหมดแล้ว\n";
    echo "✅ ตรวจสอบการเชื่อมต่อฐานข้อมูลแล้ว\n";
    echo "✅ ทดสอบการเขียน/อ่านข้อมูลแล้ว\n";
    echo "✅ ตรวจสอบ File Permissions แล้ว\n\n";
    
    echo "📝 คำแนะนำต่อไป:\n";
    echo "1. รีเฟรชเบราว์เซอร์ด้วย Ctrl+F5\n";
    echo "2. ลองเพิ่ม/แก้ไข/ลบข้อมูลผ่านระบบ Admin\n";
    echo "3. ตรวจสอบว่าข้อมูลเปลี่ยนแปลงในฐานข้อมูล\n";
    echo "4. หากยังมีปัญหา ให้ตรวจสอบ Log ใน storage/logs/\n\n";

} catch (Exception $e) {
    echo "❌ เกิดข้อผิดพลาด: " . $e->getMessage() . "\n";
    echo "📍 ไฟล์: " . $e->getFile() . " บรรทัด: " . $e->getLine() . "\n\n";
    
    echo "🔧 วิธีแก้ไขเพิ่มเติม:\n";
    echo "1. ตรวจสอบไฟล์ .env ว่าตั้งค่าฐานข้อมูลถูกต้อง\n";
    echo "2. ตรวจสอบว่า MySQL/MariaDB ทำงานอยู่\n";
    echo "3. ตรวจสอบ Log ใน storage/logs/laravel.log\n";
    echo "4. รัน: php artisan config:cache\n";
    echo "5. รัน: php artisan migrate:fresh --seed (ระวัง: จะลบข้อมูลทั้งหมด)\n";
}

echo "\n========================================\n";
echo "   การแก้ไขเสร็จสิ้น\n";
echo "========================================\n";
?>
