@if ($paginator->hasPages())
<!-- Pagination Container - จัดกลางและใช้งานง่าย -->
<div class="pagination-container">
    <!-- ข้อมูลสถิติ -->
    <div class="pagination-info text-center mb-3">
        <small class="text-muted">
            <i class="fas fa-info-circle me-1"></i>
            แสดง <strong>{{ $paginator->firstItem() ?? 0 }}</strong> ถึง <strong>{{ $paginator->lastItem() ?? 0 }}</strong>
            จากทั้งหมด <strong>{{ $paginator->total() }}</strong> รายการ
        </small>
    </div>

    <!-- Pagination Links - จัดกลาง -->
    <div class="d-flex justify-content-center align-items-center">
        <nav aria-label="Pagination">
            <ul class="pagination pagination-modern mb-0">
                {{-- Previous Page Link --}}
                @if ($paginator->onFirstPage())
                    <li class="page-item disabled">
                        <span class="page-link">
                            <i class="fas fa-chevron-left"></i>
                            <span class="d-none d-sm-inline ms-1">ก่อนหน้า</span>
                        </span>
                    </li>
                @else
                    <li class="page-item">
                        <a class="page-link" href="{{ $paginator->previousPageUrl() }}">
                            <i class="fas fa-chevron-left"></i>
                            <span class="d-none d-sm-inline ms-1">ก่อนหน้า</span>
                        </a>
                    </li>
                @endif

                {{-- Page Numbers --}}
                @php
                    $start = max(1, $paginator->currentPage() - 2);
                    $end = min($paginator->lastPage(), $paginator->currentPage() + 2);
                @endphp

                {{-- First Page --}}
                @if ($start > 1)
                    <li class="page-item">
                        <a class="page-link" href="{{ $paginator->url(1) }}">1</a>
                    </li>
                    @if ($start > 2)
                        <li class="page-item disabled">
                            <span class="page-link">...</span>
                        </li>
                    @endif
                @endif

                {{-- Page Numbers --}}
                @for ($page = $start; $page <= $end; $page++)
                    @if ($page == $paginator->currentPage())
                        <li class="page-item active">
                            <span class="page-link">{{ $page }}</span>
                        </li>
                    @else
                        <li class="page-item">
                            <a class="page-link" href="{{ $paginator->url($page) }}">{{ $page }}</a>
                        </li>
                    @endif
                @endfor

                {{-- Last Page --}}
                @if ($end < $paginator->lastPage())
                    @if ($end < $paginator->lastPage() - 1)
                        <li class="page-item disabled">
                            <span class="page-link">...</span>
                        </li>
                    @endif
                    <li class="page-item">
                        <a class="page-link" href="{{ $paginator->url($paginator->lastPage()) }}">{{ $paginator->lastPage() }}</a>
                    </li>
                @endif

                {{-- Next Page Link --}}
                @if ($paginator->hasMorePages())
                    <li class="page-item">
                        <a class="page-link" href="{{ $paginator->nextPageUrl() }}">
                            <span class="d-none d-sm-inline me-1">ถัดไป</span>
                            <i class="fas fa-chevron-right"></i>
                        </a>
                    </li>
                @else
                    <li class="page-item disabled">
                        <span class="page-link">
                            <span class="d-none d-sm-inline me-1">ถัดไป</span>
                            <i class="fas fa-chevron-right"></i>
                        </span>
                    </li>
                @endif
            </ul>
        </nav>
    </div>

    <!-- หน้าปัจจุบัน -->
    <div class="text-center mt-2">
        <small class="text-muted">
            หน้า {{ $paginator->currentPage() }} จาก {{ $paginator->lastPage() }}
        </small>
    </div>
</div>

<style>
/* Modern Pagination Styles */
.pagination-container {
    margin: 2rem 0;
    padding: 1.5rem;
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.pagination-info {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    padding: 10px 16px;
    font-size: 14px;
    color: #64748b;
    display: inline-block;
}

.pagination-modern {
    gap: 4px;
    justify-content: center;
}

.pagination-modern .page-item {
    margin: 0;
}

.pagination-modern .page-link {
    border: 2px solid #e2e8f0;
    border-radius: 10px;
    color: #475569;
    padding: 10px 14px;
    font-size: 14px;
    font-weight: 600;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    background: #fff;
    min-width: 44px;
    height: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    position: relative;
    overflow: hidden;
}

.pagination-modern .page-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.1), transparent);
    transition: left 0.5s;
}

.pagination-modern .page-link:hover::before {
    left: 100%;
}

.pagination-modern .page-link:hover {
    background: #3b82f6;
    border-color: #3b82f6;
    color: white;
    transform: translateY(-2px) scale(1.05);
    box-shadow: 0 8px 25px rgba(59, 130, 246, 0.3);
}

.pagination-modern .page-item.active .page-link {
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    border-color: #3b82f6;
    color: white;
    transform: translateY(-1px);
    box-shadow: 0 6px 20px rgba(59, 130, 246, 0.4);
}

.pagination-modern .page-item.active .page-link:hover {
    transform: translateY(-2px) scale(1.05);
    box-shadow: 0 8px 25px rgba(59, 130, 246, 0.5);
}

.pagination-modern .page-item.disabled .page-link {
    background: #f1f5f9;
    border-color: #e2e8f0;
    color: #94a3b8;
    cursor: not-allowed;
    opacity: 0.6;
}

.pagination-modern .page-item.disabled .page-link:hover {
    background: #f1f5f9;
    border-color: #e2e8f0;
    color: #94a3b8;
    transform: none;
    box-shadow: none;
}

/* Responsive Design */
@media (max-width: 768px) {
    .pagination-container {
        margin: 1rem 0;
        padding: 1rem;
    }

    .pagination-info {
        font-size: 12px;
        padding: 8px 12px;
    }

    .pagination-modern .page-link {
        padding: 8px 10px;
        font-size: 12px;
        min-width: 36px;
        height: 36px;
    }

    .pagination-modern {
        gap: 2px;
    }
}

@media (max-width: 480px) {
    .pagination-modern .page-link {
        padding: 6px 8px;
        font-size: 11px;
        min-width: 32px;
        height: 32px;
    }
}

/* Loading Animation */
.pagination-loading {
    opacity: 0.7;
    pointer-events: none;
}

.pagination-loading .page-link {
    position: relative;
}

.pagination-loading .page-link::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 16px;
    height: 16px;
    margin: -8px 0 0 -8px;
    border: 2px solid transparent;
    border-top: 2px solid currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Smooth Transitions */
.pagination-modern .page-link {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.pagination-modern .page-item:nth-child(odd) .page-link:hover {
    animation: bounce 0.6s ease-in-out;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(-2px) scale(1.05);
    }
    40% {
        transform: translateY(-4px) scale(1.08);
    }
    60% {
        transform: translateY(-2px) scale(1.05);
    }
}
</style>

<script>
// Enhanced Pagination with Smooth Loading
document.addEventListener('DOMContentLoaded', function() {
    const paginationContainer = document.querySelector('.pagination-container');
    const paginationLinks = document.querySelectorAll('.pagination-modern .page-link[href]');

    // Add smooth loading for pagination links
    paginationLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            // Prevent multiple clicks
            if (this.classList.contains('loading')) return;

            // Add loading state
            this.classList.add('loading');
            paginationContainer.classList.add('pagination-loading');

            const originalContent = this.innerHTML;
            this.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';

            // Show loading message
            const loadingMsg = document.createElement('div');
            loadingMsg.className = 'text-center mt-2 loading-message';
            loadingMsg.innerHTML = '<small class="text-muted"><i class="fas fa-circle-notch fa-spin me-1"></i>กำลังโหลดหน้าถัดไป...</small>';
            paginationContainer.appendChild(loadingMsg);

            // If navigation takes too long, restore original state
            setTimeout(() => {
                if (this.classList.contains('loading')) {
                    this.classList.remove('loading');
                    this.innerHTML = originalContent;
                    paginationContainer.classList.remove('pagination-loading');
                    const msg = paginationContainer.querySelector('.loading-message');
                    if (msg) msg.remove();
                }
            }, 5000);
        });
    });

    // Add keyboard navigation
    document.addEventListener('keydown', function(e) {
        if (e.target.tagName === 'INPUT' || e.target.tagName === 'TEXTAREA') return;

        const currentPage = document.querySelector('.pagination-modern .page-item.active .page-link');
        if (!currentPage) return;

        let targetLink = null;

        // Left arrow - Previous page
        if (e.key === 'ArrowLeft') {
            const prevItem = currentPage.closest('.page-item').previousElementSibling;
            if (prevItem && !prevItem.classList.contains('disabled')) {
                targetLink = prevItem.querySelector('.page-link[href]');
            }
        }

        // Right arrow - Next page
        if (e.key === 'ArrowRight') {
            const nextItem = currentPage.closest('.page-item').nextElementSibling;
            if (nextItem && !nextItem.classList.contains('disabled')) {
                targetLink = nextItem.querySelector('.page-link[href]');
            }
        }

        if (targetLink) {
            e.preventDefault();
            targetLink.click();
        }
    });

    // Add smooth scroll to top after page change
    if (window.location.search.includes('page=')) {
        setTimeout(() => {
            const targetElement = document.querySelector('.pagination-container') || document.querySelector('main');
            if (targetElement) {
                targetElement.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        }, 100);
    }

    // Add hover effects
    paginationLinks.forEach(link => {
        link.addEventListener('mouseenter', function() {
            if (!this.classList.contains('loading')) {
                this.style.transform = 'translateY(-2px) scale(1.05)';
            }
        });

        link.addEventListener('mouseleave', function() {
            if (!this.classList.contains('loading')) {
                this.style.transform = '';
            }
        });
    });
});

// Add page transition effects
window.addEventListener('beforeunload', function() {
    const paginationContainer = document.querySelector('.pagination-container');
    if (paginationContainer) {
        paginationContainer.style.opacity = '0.5';
        paginationContainer.style.transform = 'scale(0.95)';
    }
});
</script>
@endif
