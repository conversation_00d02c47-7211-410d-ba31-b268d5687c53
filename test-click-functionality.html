<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ทดสอบฟังก์ชันคลิก</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .test-card {
            transition: all 0.3s ease;
            border: none;
            overflow: hidden;
            cursor: pointer;
        }

        .test-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15) !important;
        }

        .test-card .card-body {
            position: relative;
            z-index: 5;
        }

        .card-hover-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            opacity: 0;
            transition: opacity 0.3s ease;
            pointer-events: none;
        }

        .test-card:hover .card-hover-overlay {
            opacity: 1;
        }

        .btn-hover-effect {
            transition: all 0.3s ease;
            position: relative;
            z-index: 10;
        }

        .btn-hover-effect:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        .card-image-container {
            position: relative;
            height: 200px;
            background: #f8f9fa;
        }

        .test-image {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
    </style>
</head>
<body>
    <div class="container py-5">
        <h1 class="text-center mb-5">ทดสอบฟังก์ชันคลิกเพื่อดูรายละเอียด</h1>
        
        <div class="row g-4">
            <div class="col-md-6 col-lg-4">
                <div class="card test-card h-100 shadow-sm">
                    <div class="card-image-container position-relative">
                        <img src="https://via.placeholder.com/300x200/007bff/ffffff?text=Service+1" 
                             class="test-image" alt="Service 1">
                        
                        <!-- Hover overlay -->
                        <div class="card-hover-overlay">
                            <div class="text-center">
                                <i class="fas fa-eye fa-2x text-white mb-2"></i>
                                <p class="text-white mb-0">คลิกเพื่อดูรายละเอียด</p>
                            </div>
                        </div>
                    </div>

                    <div class="card-body d-flex flex-column">
                        <h5 class="card-title">
                            <a href="#service1" class="text-decoration-none text-dark">บริการทดสอบ 1</a>
                        </h5>
                        <p class="card-text flex-grow-1">รายละเอียดของบริการทดสอบ 1</p>

                        <div class="mt-auto">
                            <div class="d-grid gap-2">
                                <a href="#service1-detail" class="btn btn-outline-primary btn-hover-effect">
                                    <i class="fas fa-eye me-2"></i>ดูรายละเอียด
                                </a>
                                <a href="#contact" class="btn btn-primary btn-hover-effect">
                                    <i class="fas fa-envelope me-2"></i>ติดต่อสอบถาม
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-6 col-lg-4">
                <div class="card test-card h-100 shadow-sm">
                    <div class="card-image-container position-relative">
                        <img src="https://via.placeholder.com/300x200/28a745/ffffff?text=Package+1" 
                             class="test-image" alt="Package 1">
                        
                        <!-- Hover overlay -->
                        <div class="card-hover-overlay">
                            <div class="text-center">
                                <i class="fas fa-eye fa-2x text-white mb-2"></i>
                                <p class="text-white mb-0">คลิกเพื่อดูรายละเอียด</p>
                            </div>
                        </div>
                    </div>

                    <div class="card-body d-flex flex-column">
                        <h5 class="card-title">
                            <a href="#package1" class="text-decoration-none text-dark">แพคเกจทดสอบ 1</a>
                        </h5>
                        <p class="card-text flex-grow-1">รายละเอียดของแพคเกจทดสอบ 1</p>

                        <div class="mt-auto">
                            <div class="d-grid gap-2">
                                <a href="#package1-detail" class="btn btn-outline-primary btn-hover-effect">
                                    <i class="fas fa-eye me-2"></i>ดูรายละเอียด
                                </a>
                                <a href="#contact" class="btn btn-primary btn-hover-effect">
                                    <i class="fas fa-envelope me-2"></i>ติดต่อสอบถาม
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="mt-5">
            <h3>ผลการทดสอบ:</h3>
            <div id="test-results" class="alert alert-info">
                คลิกที่การ์ดหรือปุ่มเพื่อดูผลการทดสอบ
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const resultsDiv = document.getElementById('test-results');
            
            // Test card click functionality
            document.querySelectorAll('.test-card').forEach(function(card, index) {
                card.addEventListener('click', function(e) {
                    // Don't navigate if clicking on the button
                    if (e.target.closest('.btn')) {
                        return;
                    }

                    const title = card.querySelector('.card-title a').textContent;
                    resultsDiv.innerHTML = `<strong>การ์ดถูกคลิก:</strong> ${title}`;
                    resultsDiv.className = 'alert alert-success';
                });
            });

            // Test button clicks
            document.querySelectorAll('.btn').forEach(function(btn) {
                btn.addEventListener('click', function(e) {
                    e.preventDefault();
                    const btnText = this.textContent.trim();
                    resultsDiv.innerHTML = `<strong>ปุ่มถูกคลิก:</strong> ${btnText}`;
                    resultsDiv.className = 'alert alert-warning';
                });
            });
        });
    </script>
</body>
</html>
