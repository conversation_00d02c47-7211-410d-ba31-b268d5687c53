<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('activity_images', function (Blueprint $table) {
            // Add new columns
            $table->string('alt_text')->nullable()->after('image_path');
            $table->text('description')->nullable()->after('alt_text');
        });

        // Migrate data from caption to alt_text and description
        DB::statement('UPDATE activity_images SET alt_text = caption, description = caption WHERE caption IS NOT NULL');

        Schema::table('activity_images', function (Blueprint $table) {
            // Drop old column
            $table->dropColumn('caption');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('activity_images', function (Blueprint $table) {
            // Add back caption column
            $table->string('caption')->nullable()->after('image_path');
        });

        // Migrate data back from alt_text to caption
        DB::statement('UPDATE activity_images SET caption = alt_text WHERE alt_text IS NOT NULL');

        Schema::table('activity_images', function (Blueprint $table) {
            // Drop new columns
            $table->dropColumn(['alt_text', 'description']);
        });
    }
};
