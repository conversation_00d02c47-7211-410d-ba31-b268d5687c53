<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>สร้างรูปภาพแบนเนอร์ตัวอย่าง</title>
    <style>
        body {
            font-family: 'Kanit', sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        .banner {
            width: 1200px;
            height: 400px;
            margin: 20px 0;
            position: relative;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .banner1 {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .banner2 {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        }
        .banner3 {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }
        .banner-content {
            text-align: center;
            color: white;
            z-index: 2;
            position: relative;
        }
        .banner h1 {
            font-size: 3.5rem;
            margin: 0 0 20px 0;
            font-weight: 700;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
        }
        .banner p {
            font-size: 1.8rem;
            margin: 0;
            opacity: 0.95;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
        }
        .banner::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.3);
            z-index: 1;
        }
        .download-section {
            margin: 30px 0;
            padding: 20px;
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .download-btn {
            background: #007bff;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 10px;
            font-size: 16px;
            font-weight: 500;
        }
        .download-btn:hover {
            background: #0056b3;
        }
        .instructions {
            background: #e3f2fd;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
            border-left: 4px solid #2196f3;
        }
        .instructions h3 {
            margin-top: 0;
            color: #1976d2;
        }
    </style>
    <link href="https://fonts.googleapis.com/css2?family=Kanit:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <div class="instructions">
        <h3>วิธีการใช้งาน</h3>
        <ol>
            <li>คลิกปุ่ม "ดาวน์โหลด" ใต้แต่ละแบนเนอร์</li>
            <li>รูปภาพจะถูกดาวน์โหลดไปยังโฟลเดอร์ Downloads</li>
            <li>ย้ายไฟล์ไปยัง <code>public/storage/banners/</code></li>
            <li>หรือใช้หน้า admin เพื่ออัปโหลดรูปภาพใหม่</li>
        </ol>
    </div>

    <h1>รูปภาพแบนเนอร์สำหรับแต่ละหน้า</h1>

    <!-- แบนเนอร์หน้าหลัก -->
    <div class="banner" id="home-banner" style="background: linear-gradient(135deg, #4338ca 0%, #dc2677 100%);">
        <div class="banner-content">
            <h1>หน้าหลัก - Home</h1>
            <p>ยินดีต้อนรับสู่เว็บไซต์ของเรา</p>
        </div>
    </div>
    <div class="download-section">
        <button class="download-btn" onclick="downloadBanner('home-banner', 'home-banner.jpg')">
            📥 ดาวน์โหลดแบนเนอร์หน้าหลัก
        </button>
        <span>ขนาด: 1200x400 พิกเซล</span>
    </div>

    <!-- แบนเนอร์หน้าบริการ -->
    <div class="banner" id="services-banner" style="background: linear-gradient(135deg, #22c1c3 0%, #fdbb2d 100%);">
        <div class="banner-content">
            <h1>หน้าบริการ - Services</h1>
            <p>บริการที่ครบครันและมีคุณภาพ</p>
        </div>
    </div>
    <div class="download-section">
        <button class="download-btn" onclick="downloadBanner('services-banner', 'services-banner.jpg')">
            📥 ดาวน์โหลดแบนเนอร์หน้าบริการ
        </button>
        <span>ขนาด: 1200x400 พิกเซล</span>
    </div>

    <!-- แบนเนอร์หน้าแพ็คเกจ -->
    <div class="banner" id="packages-banner" style="background: linear-gradient(135deg, #833ab4 0%, #fd1d1d 100%);">
        <div class="banner-content">
            <h1>หน้าแพ็คเกจ - Packages</h1>
            <p>แพ็คเกจที่เหมาะสมกับทุกความต้องการ</p>
        </div>
    </div>
    <div class="download-section">
        <button class="download-btn" onclick="downloadBanner('packages-banner', 'packages-banner.jpg')">
            📥 ดาวน์โหลดแบนเนอร์หน้าแพ็คเกจ
        </button>
        <span>ขนาด: 1200x400 พิกเซล</span>
    </div>

    <!-- แบนเนอร์หน้าผลงาน -->
    <div class="banner" id="activities-banner" style="background: linear-gradient(135deg, #485563 0%, #29323c 100%);">
        <div class="banner-content">
            <h1>หน้าผลงาน - Activities</h1>
            <p>ผลงานและกิจกรรมที่ผ่านมา</p>
        </div>
    </div>
    <div class="download-section">
        <button class="download-btn" onclick="downloadBanner('activities-banner', 'activities-banner.jpg')">
            📥 ดาวน์โหลดแบนเนอร์หน้าผลงาน
        </button>
        <span>ขนาด: 1200x400 พิกเซล</span>
    </div>

    <!-- แบนเนอร์หน้าติดต่อเรา -->
    <div class="banner" id="contact-banner" style="background: linear-gradient(135deg, #ff5e4d 0%, #ff9a00 100%);">
        <div class="banner-content">
            <h1>หน้าติดต่อเรา - Contact</h1>
            <p>ติดต่อเราได้ตลอด 24 ชั่วโมง</p>
        </div>
    </div>
    <div class="download-section">
        <button class="download-btn" onclick="downloadBanner('contact-banner', 'contact-banner.jpg')">
            📥 ดาวน์โหลดแบนเนอร์หน้าติดต่อเรา
        </button>
        <span>ขนาด: 1200x400 พิกเซล</span>
    </div>

    <!-- แบนเนอร์ทุกหน้า -->
    <div class="banner" id="global-banner" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
        <div class="banner-content">
            <h1>แบนเนอร์ทุกหน้า - Global</h1>
            <p>แบนเนอร์ที่แสดงในทุกหน้า</p>
        </div>
    </div>
    <div class="download-section">
        <button class="download-btn" onclick="downloadBanner('global-banner', 'global-banner.jpg')">
            📥 ดาวน์โหลดแบนเนอร์ทุกหน้า
        </button>
        <span>ขนาด: 1200x400 พิกเซล</span>
    </div>

    <div class="instructions">
        <h3>หมายเหตุ</h3>
        <p>หลังจากดาวน์โหลดรูปภาพแล้ว ให้ย้ายไฟล์ไปยังโฟลเดอร์ <code>public/storage/banners/</code> 
        หรือใช้หน้า admin เพื่ออัปโหลดรูปภาพใหม่ที่ <a href="/admin/banners" target="_blank">จัดการแบนเนอร์</a></p>
    </div>

    <script src="https://html2canvas.hertzen.com/dist/html2canvas.min.js"></script>
    <script>
        function downloadBanner(elementId, filename) {
            const element = document.getElementById(elementId);
            
            // แสดงข้อความกำลังสร้าง
            const btn = event.target;
            const originalText = btn.innerHTML;
            btn.innerHTML = '⏳ กำลังสร้างรูปภาพ...';
            btn.disabled = true;
            
            html2canvas(element, {
                width: 1200,
                height: 400,
                scale: 2,
                useCORS: true,
                allowTaint: true
            }).then(canvas => {
                const link = document.createElement('a');
                link.download = filename;
                link.href = canvas.toDataURL('image/jpeg', 0.9);
                link.click();
                
                // คืนค่าปุ่ม
                btn.innerHTML = originalText;
                btn.disabled = false;
                
                // แสดงข้อความสำเร็จ
                btn.innerHTML = '✅ ดาวน์โหลดแล้ว';
                setTimeout(() => {
                    btn.innerHTML = originalText;
                }, 2000);
            }).catch(error => {
                console.error('Error:', error);
                btn.innerHTML = '❌ เกิดข้อผิดพลาด';
                btn.disabled = false;
                setTimeout(() => {
                    btn.innerHTML = originalText;
                }, 2000);
            });
        }
    </script>
</body>
</html>
