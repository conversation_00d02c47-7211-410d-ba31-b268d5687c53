# 📊 Mermaid Diagrams Code - ระบบบริการจัดงานศพ

## 🎯 วิธีใช้งาน
1. Copy โค้ดด้านล่าง
2. ไปที่ https://mermaid.live/
3. วางโค้ดลงในช่อง
4. คลิก "Actions" → "Download PNG" เพื่อบันทึกเป็นรูปภาพ

---

## 📋 Diagram 1: E-R Diagram - โครงสร้างฐานข้อมูล

```mermaid
erDiagram
    %% ตาราง Users (ผู้ดูแลระบบ)
    USERS {
        bigint id PK "รหัสผู้ดูแล"
        varchar name "ชื่อผู้ดูแล"
        varchar email UK "อีเมล"
        varchar password "รหัสผ่าน"
        timestamp created_at "วันที่สร้าง"
        timestamp updated_at "วันที่แก้ไข"
    }

    %% ตาราง Services (บริการ)
    SERVICES {
        bigint id PK "รหัสบริการ"
        varchar title "ชื่อบริการ"
        text description "คำอธิบาย"
        text details "รายละเอียด"
        varchar image "รูปภาพหลัก"
        boolean is_active "สถานะใช้งาน"
        int sort_order "ลำดับแสดงผล"
        timestamp created_at "วันที่สร้าง"
        timestamp updated_at "วันที่แก้ไข"
    }

    %% ตาราง Service Images (รูปภาพบริการ)
    SERVICE_IMAGES {
        bigint id PK "รหัสรูปภาพ"
        bigint service_id FK "รหัสบริการ"
        varchar image_path "ที่อยู่ไฟล์"
        varchar alt_text "ข้อความทดแทน"
        text description "คำอธิบายรูป"
        boolean is_cover "รูปหน้าปก"
        int sort_order "ลำดับแสดงผล"
        timestamp created_at "วันที่อัปโหลด"
        timestamp updated_at "วันที่แก้ไข"
    }

    %% ตาราง Packages (แพ็คเกจ)
    PACKAGES {
        bigint id PK "รหัสแพ็คเกจ"
        varchar title "ชื่อแพ็คเกจ"
        text description "คำอธิบาย"
        text details "รายละเอียด"
        varchar price "ราคา"
        varchar image "รูปภาพ"
        boolean is_active "สถานะใช้งาน"
        int sort_order "ลำดับแสดงผล"
        timestamp created_at "วันที่สร้าง"
        timestamp updated_at "วันที่แก้ไข"
    }

    %% ตาราง Activities (ผลงาน)
    ACTIVITIES {
        bigint id PK "รหัสผลงาน"
        varchar title "ชื่อผลงาน"
        text description "คำอธิบาย"
        text details "รายละเอียด"
        varchar image "รูปภาพหลัก"
        boolean is_active "สถานะใช้งาน"
        int sort_order "ลำดับแสดงผล"
        timestamp created_at "วันที่สร้าง"
        timestamp updated_at "วันที่แก้ไข"
    }

    %% ตาราง Activity Images (รูปภาพผลงาน)
    ACTIVITY_IMAGES {
        bigint id PK "รหัสรูปภาพ"
        bigint activity_id FK "รหัสผลงาน"
        varchar image_path "ที่อยู่ไฟล์"
        varchar alt_text "ข้อความทดแทน"
        text description "คำอธิบายรูป"
        boolean is_cover "รูปหน้าปก"
        int sort_order "ลำดับแสดงผล"
        timestamp created_at "วันที่อัปโหลด"
        timestamp updated_at "วันที่แก้ไข"
    }

    %% ตาราง Contacts (การติดต่อ)
    CONTACTS {
        bigint id PK "รหัสการติดต่อ"
        varchar name "ชื่อผู้ติดต่อ"
        varchar email "อีเมล"
        varchar phone "เบอร์โทร"
        varchar subject "หัวข้อ"
        text message "ข้อความ"
        boolean is_read "สถานะการอ่าน"
        timestamp created_at "วันที่ส่ง"
        timestamp updated_at "วันที่แก้ไข"
    }

    %% ตาราง Banners (แบนเนอร์)
    BANNERS {
        bigint id PK "รหัสแบนเนอร์"
        varchar title "ชื่อแบนเนอร์"
        text description "คำอธิบาย"
        varchar image_path "ที่อยู่ไฟล์"
        json display_pages "หน้าที่แสดง"
        boolean is_active "สถานะใช้งาน"
        int sort_order "ลำดับแสดงผล"
        timestamp created_at "วันที่สร้าง"
        timestamp updated_at "วันที่แก้ไข"
    }

    %% ตาราง Site Settings (การตั้งค่า)
    SITE_SETTINGS {
        bigint id PK "รหัสการตั้งค่า"
        varchar key UK "คีย์การตั้งค่า"
        text value "ค่าการตั้งค่า"
        text description "คำอธิบาย"
        timestamp created_at "วันที่สร้าง"
        timestamp updated_at "วันที่แก้ไข"
    }

    %% ตาราง Migrations (ระบบ Laravel)
    MIGRATIONS {
        int id PK "รหัสลำดับ"
        varchar migration "ชื่อไฟล์ migration"
        int batch "กลุ่มการรัน"
    }

    %% ความสัมพันธ์ระหว่างตาราง
    SERVICES ||--o{ SERVICE_IMAGES : "มี"
    ACTIVITIES ||--o{ ACTIVITY_IMAGES : "มี"
```

---

## 📋 Diagram 2: ระบบการจัดการของผู้ดูแลระบบ

```mermaid
graph TD
    %% ผู้ดูแลระบบ
    A[👤 ผู้ดูแลระบบ<br/>USERS] --> B[🔐 เข้าสู่ระบบ<br/>Login System]
    
    %% Dashboard หลัก
    B --> C[📊 หน้า Dashboard<br/>ภาพรวมระบบ]
    
    %% การจัดการต่างๆ
    C --> D[🛠️ จัดการบริการ<br/>SERVICES]
    C --> E[📦 จัดการแพ็คเกจ<br/>PACKAGES]
    C --> F[🎨 จัดการผลงาน<br/>ACTIVITIES]
    C --> G[📞 จัดการข้อความ<br/>CONTACTS]
    C --> H[🎨 จัดการแบนเนอร์<br/>BANNERS]
    C --> I[⚙️ การตั้งค่าเว็บ<br/>SITE_SETTINGS]
    
    %% การจัดการบริการ
    D --> D1[📝 เพิ่ม/แก้ไข/ลบ บริการ]
    D --> D2[🖼️ จัดการรูปภาพ<br/>SERVICE_IMAGES]
    D2 --> D2A[📸 อัปโหลดรูปใหม่]
    D2 --> D2B[🏆 กำหนดรูปหน้าปก]
    D2 --> D2C[🔢 เรียงลำดับรูป]
    D2 --> D2D[🗑️ ลบรูปภาพ]
    
    %% การจัดการแพ็คเกจ
    E --> E1[📝 เพิ่ม/แก้ไข/ลบ แพ็คเกจ]
    E --> E2[💰 กำหนดราคา]
    E --> E3[📸 อัปโหลดรูปภาพ]
    E --> E4[🔄 เปิด/ปิด การแสดงผล]
    
    %% การจัดการผลงาน
    F --> F1[📝 เพิ่ม/แก้ไข/ลบ ผลงาน]
    F --> F2[🖼️ จัดการรูปภาพ<br/>ACTIVITY_IMAGES]
    F2 --> F2A[📸 อัปโหลดรูปใหม่]
    F2 --> F2B[🏆 กำหนดรูปหน้าปก]
    F2 --> F2C[🔢 เรียงลำดับรูป]
    F2 --> F2D[🗑️ ลบรูปภาพ]
    
    %% การจัดการข้อความ
    G --> G1[📋 ดูรายการข้อความ]
    G --> G2[👁️ อ่านรายละเอียด]
    G --> G3[✅ ทำเครื่องหมายอ่านแล้ว]
    G --> G4[📧 ตอบกลับทางอีเมล]
    G --> G5[📞 โทรหาลูกค้า]
    G --> G6[🗑️ ลบข้อความ]
    
    %% การจัดการแบนเนอร์
    H --> H1[📝 เพิ่ม/แก้ไข/ลบ แบนเนอร์]
    H --> H2[📸 อัปโหลดรูปภาพ]
    H --> H3[🎯 เลือกหน้าที่แสดง]
    H --> H4[🔢 เรียงลำดับการแสดง]
    H --> H5[🔄 เปิด/ปิด การแสดงผล]
    
    %% การตั้งค่าเว็บไซต์
    I --> I1[🏢 ชื่อเว็บไซต์]
    I --> I2[📞 เบอร์โทรศัพท์]
    I --> I3[📧 อีเมลติดต่อ]
    I --> I4[📍 ที่อยู่]
    I --> I5[📱 Facebook/Line]
    I --> I6[📝 คำอธิบายเว็บ]
    
    %% สีของกล่อง
    classDef userClass fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef systemClass fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    classDef manageClass fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px
    classDef actionClass fill:#fff3e0,stroke:#e65100,stroke-width:2px
    
    class A userClass
    class B,C systemClass
    class D,E,F,G,H,I manageClass
    class D1,D2,D2A,D2B,D2C,D2D,E1,E2,E3,E4,F1,F2,F2A,F2B,F2C,F2D,G1,G2,G3,G4,G5,G6,H1,H2,H3,H4,H5,I1,I2,I3,I4,I5,I6 actionClass
```

---

## 📋 Diagram 3: Data Flow Diagram - การไหลของข้อมูล

```mermaid
graph LR
    %% ผู้ใช้งาน
    subgraph "👥 ผู้ใช้งาน"
        U1[🌐 ลูกค้า<br/>Website Visitor]
        U2[👤 ผู้ดูแลระบบ<br/>Admin]
    end
    
    %% ระบบหน้าเว็บ
    subgraph "🌐 ระบบหน้าเว็บ"
        W1[🏠 หน้าหลัก<br/>Home Page]
        W2[🛠️ หน้าบริการ<br/>Services Page]
        W3[📦 หน้าแพ็คเกจ<br/>Packages Page]
        W4[🎨 หน้าผลงาน<br/>Activities Page]
        W5[📞 หน้าติดต่อ<br/>Contact Page]
    end
    
    %% ระบบ Admin
    subgraph "🔧 ระบบ Admin"
        A1[🔐 Login System]
        A2[📊 Dashboard]
        A3[🛠️ Services Management]
        A4[📦 Packages Management]
        A5[🎨 Activities Management]
        A6[📞 Contacts Management]
        A7[🎨 Banners Management]
        A8[⚙️ Settings Management]
    end
    
    %% ฐานข้อมูล
    subgraph "💾 ฐานข้อมูล"
        DB1[(👤 USERS)]
        DB2[(🛠️ SERVICES)]
        DB3[(🖼️ SERVICE_IMAGES)]
        DB4[(📦 PACKAGES)]
        DB5[(🎨 ACTIVITIES)]
        DB6[(📸 ACTIVITY_IMAGES)]
        DB7[(📞 CONTACTS)]
        DB8[(🎨 BANNERS)]
        DB9[(⚙️ SITE_SETTINGS)]
        DB10[(🗂️ MIGRATIONS)]
    end
    
    %% การไหลของข้อมูล - ลูกค้า
    U1 --> W1
    U1 --> W2
    U1 --> W3
    U1 --> W4
    U1 --> W5
    
    %% การไหลของข้อมูล - Admin
    U2 --> A1
    A1 --> A2
    A2 --> A3
    A2 --> A4
    A2 --> A5
    A2 --> A6
    A2 --> A7
    A2 --> A8
    
    %% การอ่านข้อมูลจากฐานข้อมูล
    W1 -.->|อ่าน| DB2
    W1 -.->|อ่าน| DB5
    W1 -.->|อ่าน| DB8
    W1 -.->|อ่าน| DB9
    
    W2 -.->|อ่าน| DB2
    W2 -.->|อ่าน| DB3
    W2 -.->|อ่าน| DB8
    W2 -.->|อ่าน| DB9
    
    W3 -.->|อ่าน| DB4
    W3 -.->|อ่าน| DB9
    
    W4 -.->|อ่าน| DB5
    W4 -.->|อ่าน| DB6
    W4 -.->|อ่าน| DB9
    
    W5 -.->|อ่าน| DB9
    W5 -->|เขียน| DB7
    
    %% การจัดการข้อมูลโดย Admin
    A1 -.->|ตรวจสอบ| DB1
    
    A3 -->|CRUD| DB2
    A3 -->|CRUD| DB3
    
    A4 -->|CRUD| DB4
    
    A5 -->|CRUD| DB5
    A5 -->|CRUD| DB6
    
    A6 -.->|อ่าน/ลบ| DB7
    
    A7 -->|CRUD| DB8
    
    A8 -->|CRUD| DB9
    
    %% ความสัมพันธ์ระหว่างตาราง
    DB2 -.->|1:N| DB3
    DB5 -.->|1:N| DB6
    
    %% สีของกล่อง
    classDef userClass fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
    classDef webClass fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    classDef adminClass fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    classDef dbClass fill:#fce4ec,stroke:#c2185b,stroke-width:2px
    
    class U1,U2 userClass
    class W1,W2,W3,W4,W5 webClass
    class A1,A2,A3,A4,A5,A6,A7,A8 adminClass
    class DB1,DB2,DB3,DB4,DB5,DB6,DB7,DB8,DB9,DB10 dbClass
```

---

## 🎯 วิธีใช้งาน

1. **Copy โค้ดแต่ละ Diagram** ที่ต้องการ
2. **ไปที่ https://mermaid.live/**
3. **วางโค้ด** ลงในช่องด้านซ้าย
4. **ดู Preview** ด้านขวา
5. **คลิก "Actions"** → **"Download PNG"** เพื่อบันทึก

## 📱 ทางเลือกอื่น

- **GitHub**: สร้าง .md file แล้ววางโค้ด
- **Notion**: รองรับ Mermaid diagrams
- **Obsidian**: Plugin สำหรับ Mermaid
- **VS Code**: Extension "Mermaid Preview"
