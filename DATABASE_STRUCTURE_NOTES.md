# 📋 โครงสร้างฐานข้อมูล - ระบบบริการจัดงานศพ

## 🎯 ภาพรวมระบบ
ระบบบริการจัดงานศพที่ประกอบด้วย 10 ตารางหลัก สำหรับจัดการข้อมูลบริการ ผลงาน แพ็คเกจ และการติดต่อ

---

## 📊 รายละเอียดตารางทั้งหมด

### 1. 👤 ตาราง `users` - ผู้ดูแลระบบ
**วัตถุประสงค์:** เก็บข้อมูลผู้ดูแลระบบ Admin

| ลำดับ | ชื่อฟิลด์ | ประเภทข้อมูล | คีย์ | คำอธิบาย |
|-------|-----------|---------------|------|-----------|
| 1 | id | BIGINT | PK | รหัสผู้ดูแลระบบ (Auto Increment) |
| 2 | name | VARCHAR(255) | - | ชื่อผู้ดูแลระบบ |
| 3 | email | VARCHAR(255) | UNIQUE | อีเมลสำหรับเข้าสู่ระบบ |
| 4 | password | VARCHAR(255) | - | รหัสผ่าน (เข้ารหัส) |
| 5 | created_at | TIMESTAMP | - | วันที่สร้างบัญชี |
| 6 | updated_at | TIMESTAMP | - | วันที่แก้ไขล่าสุด |

**การใช้งาน:** ระบบ Login Admin, จัดการสิทธิ์การเข้าถึง

---

### 2. 🛠️ ตาราง `services` - บริการ
**วัตถุประสงค์:** เก็บข้อมูลบริการต่างๆ ที่ให้บริการ

| ลำดับ | ชื่อฟิลด์ | ประเภทข้อมูล | คีย์ | คำอธิบาย |
|-------|-----------|---------------|------|-----------|
| 1 | id | BIGINT | PK | รหัสบริการ (Auto Increment) |
| 2 | title | VARCHAR(255) | - | ชื่อบริการ |
| 3 | description | TEXT | - | คำอธิบายบริการ |
| 4 | details | TEXT | - | รายละเอียดเพิ่มเติม |
| 5 | image | VARCHAR(255) | - | รูปภาพหลักของบริการ |
| 6 | is_active | BOOLEAN | - | สถานะการใช้งาน (1=เปิด, 0=ปิด) |
| 7 | sort_order | INT | - | ลำดับการแสดงผล |
| 8 | created_at | TIMESTAMP | - | วันที่สร้าง |
| 9 | updated_at | TIMESTAMP | - | วันที่แก้ไขล่าสุด |

**การใช้งาน:** แสดงในหน้าหลัก (6 บริการ), หน้าบริการทั้งหมด

---

### 3. 🖼️ ตาราง `service_images` - รูปภาพบริการ
**วัตถุประสงค์:** เก็บรูปภาพของแต่ละบริการ (1 บริการมีได้หลายรูป)

| ลำดับ | ชื่อฟิลด์ | ประเภทข้อมูล | คีย์ | คำอธิบาย |
|-------|-----------|---------------|------|-----------|
| 1 | id | BIGINT | PK | รหัสรูปภาพ (Auto Increment) |
| 2 | service_id | BIGINT | FK | รหัสบริการที่เป็นเจ้าของรูป |
| 3 | image_path | VARCHAR(255) | - | ที่อยู่ไฟล์รูปภาพ |
| 4 | alt_text | VARCHAR(255) | - | ข้อความทดแทนรูปภาพ |
| 5 | description | TEXT | - | คำอธิบายรูปภาพ |
| 6 | is_cover | BOOLEAN | - | รูปหน้าปก (1=ใช่, 0=ไม่ใช่) |
| 7 | sort_order | INT | - | ลำดับการแสดงผล |
| 8 | created_at | TIMESTAMP | - | วันที่อัปโหลด |
| 9 | updated_at | TIMESTAMP | - | วันที่แก้ไขล่าสุด |

**ความสัมพันธ์:** `service_images.service_id` → `services.id`
**การใช้งาน:** แกลเลอรี่ในหน้ารายละเอียดบริการ

---

### 4. 📦 ตาราง `packages` - แพ็คเกจบริการ
**วัตถุประสงค์:** เก็บข้อมูลแพ็คเกจบริการต่างๆ

| ลำดับ | ชื่อฟิลด์ | ประเภทข้อมูล | คีย์ | คำอธิบาย |
|-------|-----------|---------------|------|-----------|
| 1 | id | BIGINT | PK | รหัสแพ็คเกจ (Auto Increment) |
| 2 | title | VARCHAR(255) | - | ชื่อแพ็คเกจ |
| 3 | description | TEXT | - | คำอธิบายแพ็คเกจ |
| 4 | details | TEXT | - | รายละเอียดเพิ่มเติม |
| 5 | price | VARCHAR(255) | - | ราคา (เก็บเป็น String) |
| 6 | image | VARCHAR(255) | - | รูปภาพแพ็คเกจ |
| 7 | is_active | BOOLEAN | - | สถานะการใช้งาน |
| 8 | sort_order | INT | - | ลำดับการแสดงผล |
| 9 | created_at | TIMESTAMP | - | วันที่สร้าง |
| 10 | updated_at | TIMESTAMP | - | วันที่แก้ไขล่าสุด |

**การใช้งาน:** หน้าแพ็คเกจ, เปรียบเทียบราคา

---

### 5. 🎨 ตาราง `activities` - ผลงาน
**วัตถุประสงค์:** เก็บข้อมูลผลงานการให้บริการ

| ลำดับ | ชื่อฟิลด์ | ประเภทข้อมูล | คีย์ | คำอธิบาย |
|-------|-----------|---------------|------|-----------|
| 1 | id | BIGINT | PK | รหัสผลงาน (Auto Increment) |
| 2 | title | VARCHAR(255) | - | ชื่อผลงาน |
| 3 | description | TEXT | - | คำอธิบายผลงาน |
| 4 | details | TEXT | - | รายละเอียดเพิ่มเติม |
| 5 | image | VARCHAR(255) | - | รูปภาพหลัก (สำหรับ backward compatibility) |
| 6 | is_active | BOOLEAN | - | สถานะการแสดงผล |
| 7 | sort_order | INT | - | ลำดับการแสดงผล |
| 8 | created_at | TIMESTAMP | - | วันที่สร้าง |
| 9 | updated_at | TIMESTAMP | - | วันที่แก้ไขล่าสุด |

**การใช้งาน:** หน้าหลัก (4 ผลงานแบบสุ่ม), หน้าผลงานทั้งหมด

---

### 6. 📸 ตาราง `activity_images` - รูปภาพผลงาน
**วัตถุประสงค์:** เก็บรูปภาพของแต่ละผลงาน (1 ผลงานมีได้หลายรูป)

| ลำดับ | ชื่อฟิลด์ | ประเภทข้อมูล | คีย์ | คำอธิบาย |
|-------|-----------|---------------|------|-----------|
| 1 | id | BIGINT | PK | รหัสรูปภาพ (Auto Increment) |
| 2 | activity_id | BIGINT | FK | รหัสผลงานที่เป็นเจ้าของรูป |
| 3 | image_path | VARCHAR(255) | - | ที่อยู่ไฟล์รูปภาพ |
| 4 | alt_text | VARCHAR(255) | - | ข้อความทดแทนรูปภาพ |
| 5 | description | TEXT | - | คำอธิบายรูปภาพ |
| 6 | is_cover | BOOLEAN | - | รูปหน้าปก (1=ใช่, 0=ไม่ใช่) |
| 7 | sort_order | INT | - | ลำดับการแสดงผล |
| 8 | created_at | TIMESTAMP | - | วันที่อัปโหลด |
| 9 | updated_at | TIMESTAMP | - | วันที่แก้ไขล่าสุด |

**ความสัมพันธ์:** `activity_images.activity_id` → `activities.id`
**การใช้งาน:** แกลเลอรี่ในหน้ารายละเอียดผลงาน

---

### 7. 📞 ตาราง `contacts` - การติดต่อ
**วัตถุประสงค์:** เก็บข้อมูลการติดต่อจากลูกค้า

| ลำดับ | ชื่อฟิลด์ | ประเภทข้อมูล | คีย์ | คำอธิบาย |
|-------|-----------|---------------|------|-----------|
| 1 | id | BIGINT | PK | รหัสการติดต่อ (Auto Increment) |
| 2 | name | VARCHAR(255) | - | ชื่อผู้ติดต่อ |
| 3 | email | VARCHAR(255) | - | อีเมลผู้ติดต่อ |
| 4 | phone | VARCHAR(20) | - | เบอร์โทรศัพท์ |
| 5 | subject | VARCHAR(255) | - | หัวข้อการติดต่อ |
| 6 | message | TEXT | - | ข้อความ |
| 7 | is_read | BOOLEAN | - | สถานะการอ่าน (0=ยังไม่อ่าน, 1=อ่านแล้ว) |
| 8 | created_at | TIMESTAMP | - | วันที่ส่งข้อความ |
| 9 | updated_at | TIMESTAMP | - | วันที่แก้ไขล่าสุด |

**การใช้งาน:** ระบบ Admin ดูข้อความจากลูกค้า, แจ้งเตือนข้อความใหม่

---

### 8. 🎨 ตาราง `banners` - แบนเนอร์
**วัตถุประสงค์:** เก็บข้อมูลแบนเนอร์สไลด์โชว์

| ลำดับ | ชื่อฟิลด์ | ประเภทข้อมูล | คีย์ | คำอธิบาย |
|-------|-----------|---------------|------|-----------|
| 1 | id | BIGINT | PK | รหัสแบนเนอร์ (Auto Increment) |
| 2 | title | VARCHAR(255) | - | ชื่อแบนเนอร์ |
| 3 | description | TEXT | - | คำอธิบายแบนเนอร์ |
| 4 | image_path | VARCHAR(255) | - | ที่อยู่ไฟล์รูปภาพ |
| 5 | display_pages | JSON | - | หน้าที่ต้องการแสดง ['home', 'services'] |
| 6 | is_active | BOOLEAN | - | สถานะการแสดงผล |
| 7 | sort_order | INT | - | ลำดับการแสดงผล |
| 8 | created_at | TIMESTAMP | - | วันที่สร้าง |
| 9 | updated_at | TIMESTAMP | - | วันที่แก้ไขล่าสุด |

**การใช้งาน:** สไลด์โชว์ในหน้าหลัก, หน้าบริการ

---

### 9. ⚙️ ตาราง `site_settings` - การตั้งค่าเว็บไซต์
**วัตถุประสงค์:** เก็บการตั้งค่าทั่วไปของเว็บไซต์

| ลำดับ | ชื่อฟิลด์ | ประเภทข้อมูล | คีย์ | คำอธิบาย |
|-------|-----------|---------------|------|-----------|
| 1 | id | BIGINT | PK | รหัสการตั้งค่า (Auto Increment) |
| 2 | key | VARCHAR(255) | UNIQUE | คีย์การตั้งค่า (เช่น site_name, phone) |
| 3 | value | TEXT | - | ค่าการตั้งค่า |
| 4 | description | TEXT | - | คำอธิบายการตั้งค่า |
| 5 | created_at | TIMESTAMP | - | วันที่สร้าง |
| 6 | updated_at | TIMESTAMP | - | วันที่แก้ไขล่าสุด |

**ตัวอย่างข้อมูล:**
- `site_name` = "บริการจัดงานศพ"
- `phone` = "02-xxx-xxxx"
- `email` = "<EMAIL>"

**การใช้งาน:** แสดงในทุกหน้าของเว็บไซต์ (Header, Footer)

---

### 10. 🗂️ ตาราง `migrations` - ระบบ Laravel
**วัตถุประสงค์:** เก็บประวัติการสร้าง/แก้ไขโครงสร้างฐานข้อมูล

| ลำดับ | ชื่อฟิลด์ | ประเภทข้อมูล | คีย์ | คำอธิบาย |
|-------|-----------|---------------|------|-----------|
| 1 | id | INT | PK | รหัสลำดับการรัน |
| 2 | migration | VARCHAR(255) | - | ชื่อไฟล์ migration |
| 3 | batch | INT | - | กลุ่มการรัน migration |

**⚠️ สำคัญ:** ห้ามลบหรือแก้ไข - เป็นตารางระบบของ Laravel

---

## 🔗 ความสัมพันธ์ระหว่างตาราง

### One-to-Many Relationships:
1. **services** → **service_images** (1 บริการ : หลายรูป)
2. **activities** → **activity_images** (1 ผลงาน : หลายรูป)

### Independent Tables:
- `users` - ไม่มีความสัมพันธ์กับตารางอื่น
- `packages` - ตารางอิสระ
- `contacts` - ตารางอิสระ
- `banners` - ตารางอิสระ
- `site_settings` - ตารางอิสระ
- `migrations` - ตารางระบบ

---

## 📈 สถิติการใช้งานปัจจุบัน

| ตาราง | จำนวนข้อมูล | สถานะ |
|--------|--------------|--------|
| users | 1 รายการ | ✅ ใช้งาน |
| services | 3 รายการ | ✅ ใช้งาน |
| service_images | 3 รายการ | ✅ ใช้งาน |
| packages | 3 รายการ | ✅ ใช้งาน |
| activities | 3 รายการ | ✅ ใช้งาน |
| activity_images | 4 รายการ | ✅ ใช้งาน |
| contacts | 1 รายการ | ✅ ใช้งาน |
| banners | 1 รายการ | ✅ ใช้งาน |
| site_settings | 7 รายการ | ✅ ใช้งาน |
| migrations | 28 รายการ | ✅ ระบบ |

---

## 💡 หมายเหตุสำคัญ

### 🎯 **จุดเด่นของการออกแบบ:**
1. **แยกรูปภาพออกเป็นตารางต่างหาก** - ยืดหยุ่นในการจัดการรูปหลายรูป
2. **ใช้ JSON สำหรับ display_pages** - ยืดหยุ่นในการกำหนดหน้าแสดง
3. **มี sort_order ทุกตาราง** - ควบคุมลำดับการแสดงผลได้
4. **มี is_active ทุกตาราง** - เปิด/ปิดการแสดงผลได้

### ⚠️ **ข้อควรระวัง:**
1. **ตาราง migrations** - ห้ามลบหรือแก้ไข
2. **Foreign Key** - ระวังการลบข้อมูลที่มีความสัมพันธ์
3. **รูปภาพ** - ต้องจัดการไฟล์ในโฟลเดอร์ storage ด้วย

### 🚀 **แนวทางพัฒนาต่อ:**
1. เพิ่มระบบ SEO (meta tags)
2. เพิ่มระบบ Cache
3. เพิ่มระบบ Backup อัตโนมัติ
4. เพิ่มระบบ Log การใช้งาน
