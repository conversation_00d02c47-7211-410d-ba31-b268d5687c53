<?php
/**
 * ตรวจสอบตารางที่ไม่ได้ใช้งานในฐานข้อมูล
 * สคริปต์นี้จะตรวจสอบตารางทั้งหมดในฐานข้อมูลและแสดงผลว่าตารางไหนถูกใช้งานและไม่ได้ใช้งาน
 */

require_once __DIR__ . '/vendor/autoload.php';

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

// Bootstrap Laravel
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

echo "========================================\n";
echo "   ตรวจสอบตารางที่ไม่ได้ใช้งาน\n";
echo "========================================\n\n";

try {
    // ตรวจสอบการเชื่อมต่อฐานข้อมูล
    echo "🔍 ตรวจสอบการเชื่อมต่อฐานข้อมูล...\n";
    DB::connection()->getPdo();
    echo "✅ การเชื่อมต่อฐานข้อมูล: สำเร็จ\n\n";

    // ดึงรายชื่อตารางทั้งหมดในฐานข้อมูล
    $databaseName = config('database.connections.mysql.database');
    $tables = DB::select("SHOW TABLES");
    $tableColumn = "Tables_in_{$databaseName}";
    
    echo "📊 ตารางทั้งหมดในฐานข้อมูล '{$databaseName}':\n";
    echo "----------------------------------------\n";

    // กำหนดตารางที่ใช้งานจริงในเว็บไซต์
    $usedTables = [
        'users' => [
            'status' => 'ใช้งาน',
            'description' => 'ตารางผู้ใช้งาน - ใช้สำหรับระบบ Admin',
            'usage' => 'AuthController, AdminController'
        ],
        'services' => [
            'status' => 'ใช้งาน',
            'description' => 'ตารางบริการ - แสดงในหน้าหลักและหน้าบริการ',
            'usage' => 'HomeController, AdminController'
        ],
        'service_categories' => [
            'status' => 'ใช้งาน',
            'description' => 'ตารางหมวดหมู่บริการ - ใช้จัดกลุ่มบริการ',
            'usage' => 'HomeController, AdminController'
        ],
        'service_images' => [
            'status' => 'ใช้งาน',
            'description' => 'ตารางรูปภาพบริการ - เก็บรูปภาพของแต่ละบริการ',
            'usage' => 'AdminController'
        ],
        'packages' => [
            'status' => 'ใช้งาน',
            'description' => 'ตารางแพ็คเกจ - แสดงในหน้าแพ็คเกจ',
            'usage' => 'HomeController, AdminController'
        ],
        'activities' => [
            'status' => 'ใช้งาน',
            'description' => 'ตารางผลงาน - แสดงในหน้าหลักและหน้าผลงาน',
            'usage' => 'HomeController, AdminController'
        ],
        'activity_images' => [
            'status' => 'ใช้งาน',
            'description' => 'ตารางรูปภาพผลงาน - เก็บรูปภาพของแต่ละผลงาน',
            'usage' => 'AdminController'
        ],
        'contacts' => [
            'status' => 'ใช้งาน',
            'description' => 'ตารางการติดต่อ - เก็บข้อมูลการติดต่อจากลูกค้า',
            'usage' => 'HomeController, AdminController'
        ],
        'site_settings' => [
            'status' => 'ใช้งาน',
            'description' => 'ตารางการตั้งค่าเว็บไซต์ - เก็บข้อมูลการตั้งค่าทั่วไป',
            'usage' => 'HomeController, AdminController'
        ],
        'banners' => [
            'status' => 'ใช้งาน',
            'description' => 'ตารางแบนเนอร์ - แสดงสไลด์โชว์ในหน้าหลัก',
            'usage' => 'HomeController, BannerController'
        ],
        'migrations' => [
            'status' => 'ระบบ Laravel',
            'description' => 'ตารางระบบ Laravel - เก็บประวัติการ migrate',
            'usage' => 'Laravel Framework'
        ]
    ];

    // ตารางที่ไม่ได้ใช้งาน
    $unusedTables = [
        'password_resets' => [
            'status' => 'ไม่ได้ใช้',
            'description' => 'ตารางรีเซ็ตรหัสผ่าน - ไม่มีฟีเจอร์รีเซ็ตรหัสผ่านในเว็บไซต์',
            'reason' => 'ไม่มีระบบรีเซ็ตรหัสผ่าน'
        ],
        'failed_jobs' => [
            'status' => 'ไม่ได้ใช้',
            'description' => 'ตาราง Queue Jobs ที่ล้มเหลว - ไม่ได้ใช้ระบบ Queue',
            'reason' => 'ไม่ได้ใช้ระบบ Queue'
        ],
        'personal_access_tokens' => [
            'status' => 'ไม่ได้ใช้',
            'description' => 'ตาราง API Tokens - ไม่ได้ใช้ระบบ API',
            'reason' => 'ไม่ได้ใช้ระบบ API Authentication'
        ],
        'page_backgrounds' => [
            'status' => 'ไม่ได้ใช้',
            'description' => 'ตารางพื้นหลังหน้าเว็บ - มี Model แต่ไม่ได้ใช้งานใน Controller',
            'reason' => 'ใช้ระบบ Banner แทน'
        ],
        'page_background_images' => [
            'status' => 'ไม่ได้ใช้',
            'description' => 'ตารางรูปภาพพื้นหลัง - มี Model แต่ไม่ได้ใช้งานใน Controller',
            'reason' => 'ใช้ระบบ Banner แทน'
        ]
    ];

    $allKnownTables = array_merge($usedTables, $unusedTables);

    foreach ($tables as $table) {
        $tableName = $table->$tableColumn;
        
        if (isset($usedTables[$tableName])) {
            $info = $usedTables[$tableName];
            echo sprintf("✅ %-25s | %s\n", $tableName, $info['status']);
            echo sprintf("   📝 %s\n", $info['description']);
            echo sprintf("   🔧 ใช้ใน: %s\n\n", $info['usage']);
        } elseif (isset($unusedTables[$tableName])) {
            $info = $unusedTables[$tableName];
            echo sprintf("❌ %-25s | %s\n", $tableName, $info['status']);
            echo sprintf("   📝 %s\n", $info['description']);
            echo sprintf("   💡 เหตุผล: %s\n\n", $info['reason']);
        } else {
            echo sprintf("❓ %-25s | ไม่ทราบสถานะ\n", $tableName);
            echo sprintf("   📝 ตารางที่ไม่ได้ระบุไว้ในการตรวจสอบ\n\n");
        }
    }

    // สรุปผล
    echo "📈 สรุปผลการตรวจสอบ:\n";
    echo "----------------------------------------\n";
    
    $usedCount = 0;
    $unusedCount = 0;
    $unknownCount = 0;
    
    foreach ($tables as $table) {
        $tableName = $table->$tableColumn;
        if (isset($usedTables[$tableName])) {
            $usedCount++;
        } elseif (isset($unusedTables[$tableName])) {
            $unusedCount++;
        } else {
            $unknownCount++;
        }
    }
    
    echo "✅ ตารางที่ใช้งาน: {$usedCount} ตาราง\n";
    echo "❌ ตารางที่ไม่ได้ใช้: {$unusedCount} ตาราง\n";
    echo "❓ ตารางที่ไม่ทราบสถานะ: {$unknownCount} ตาราง\n";
    echo "📊 รวมทั้งหมด: " . count($tables) . " ตาราง\n\n";

    // คำแนะนำ
    echo "💡 คำแนะนำ:\n";
    echo "----------------------------------------\n";
    echo "🗑️ ตารางที่สามารถลบได้ (ไม่ส่งผลต่อการทำงานของเว็บไซต์):\n";
    foreach ($unusedTables as $tableName => $info) {
        if ($info['status'] === 'ไม่ได้ใช้') {
            echo "   - {$tableName}\n";
        }
    }
    
    echo "\n⚠️ ข้อควรระวัง:\n";
    echo "   - ก่อนลบตาราง ควรสำรองข้อมูลก่อน\n";
    echo "   - ตาราง migrations ไม่ควรลบ เพราะเป็นตารางระบบ\n";
    echo "   - หากมีแผนจะใช้ฟีเจอร์ในอนาคต ไม่ควรลบตาราง\n\n";

    echo "🔧 วิธีลบตาราง (ถ้าต้องการ):\n";
    echo "   1. สำรองข้อมูลก่อน: mysqldump -u username -p database_name > backup.sql\n";
    echo "   2. ลบตารางผ่าน phpMyAdmin หรือ SQL command\n";
    echo "   3. ลบไฟล์ migration ที่เกี่ยวข้อง (ถ้าต้องการ)\n\n";

} catch (Exception $e) {
    echo "❌ เกิดข้อผิดพลาด: " . $e->getMessage() . "\n";
    echo "📍 ไฟล์: " . $e->getFile() . " บรรทัด: " . $e->getLine() . "\n\n";
    
    echo "🔧 วิธีแก้ไข:\n";
    echo "1. ตรวจสอบการตั้งค่าฐานข้อมูลในไฟล์ .env\n";
    echo "2. ตรวจสอบว่า MySQL Server ทำงานอยู่\n";
    echo "3. ตรวจสอบว่าฐานข้อมูลมีอยู่จริง\n";
}

echo "\n========================================\n";
echo "   การตรวจสอบเสร็จสิ้น\n";
echo "========================================\n";
?>
